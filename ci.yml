Global:
    version: "2.0"
    group_email: <EMAIL>
Default:
    profile:
        - python3
Profiles:
    - profile:
      name: dev
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - python: 2.7.18
      build:
        command: |
            sh ./build.sh
      artifacts:
        release: true

    - profile:
      name: python3
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
          - python: 3.8.16
      build:
        command: |
          sh ./build.sh
      artifacts:
        release: true
