#!/usr/bin/env bash

set -xe

rm -rf ./output/
mkdir output
mkdir -p output/client
mkdir -p output/common
mkdir -p output/frame
mkdir -p output/server
mkdir -p output/install
mkdir -p output/scripts
mkdir -p output/buildin
mkdir -p output/adapter

cp -rf client/* output/client/
cp -rf common/* output/common/
cp -rf frame/* output/frame/
cp -rf server/* output/server/
cp -rf install/* output/install/
cp -rf scripts/* output/scripts/
cp -rf buildin/* output/buildin/
cp -rf adapter/* output/adapter
cp repocli output/
cp repocli.py output/

# 接口测试产出
tar -czf interface-tests.tar.gz interface-tests
mv interface-tests.tar.gz output

find ./output -type d -name .git | xargs -i rm -rf {}
cd output
tar cvzf repoclient.tar.gz client common frame adapter server install buildin repocli.py repocli
rm -rf client common frame server repocli.py repocli buildin adapter
