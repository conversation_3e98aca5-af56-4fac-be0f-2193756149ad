# !/usr/bin/env python2.7
"""
simple downloader client of irepo
"""

import argparse
import subprocess
import re
import json
import time
import random
import subprocess
import os
import signal


class IRepoException(Exception):
    """
    acceptable exception
    """
    BAD_ARGS = 1
    SEED_FAILED = 2
    GKO3_FAILED = 3
    CHECK_FAILED = 4
    MV_FAILED = 5
    BAD_REQUEST = 6
    TIME_OUT = 7
    UNKNOWN = 10


def timeout(timeout_calc_func):
    """
    timeout decorator
    """

    def timeout_wrap(func):
        """
        wrapper
        """

        def _handle(signum, frame):
            print('[ERROR] operation timeout.')
            raise IRepoException(IRepoException.TIME_OUT)

        def invoke_func(*args, **kwargs):
            """
            invoke real func
            """
            signal.signal(signal.SIGALRM, _handle)
            timeout_seconds = timeout_calc_func()
            signal.alarm(timeout_seconds)
            r = func(*args, **kwargs)
            signal.alarm(0)
            return r

        return invoke_func

    return timeout_wrap


class Constant(object):
    """
    const tools
    """
    TIME_OUT_SECONDS = 600
    SPEED_LIMIT = '50'
    HOST = 'http://irepotest.baidu-int.com'

    # split signal of HTTP_RESPONSE and HTTP_CODE
    SPLIT_SIGN = 'IREPO@SPLIT@WORD'
    PROTOCOL_REG = re.compile(r'irepo:\/\/(.+)\/(nodes|releases)\/([^\?]+)(?:\?path=([^&]*))?')
    DOWNLOAD_CMD = "curl -s -L -H 'IREPO-TOKEN:%(token)s' -H 'IREPO:irepo.py'" \
                   " '%(url)s' -o '%(out)s' --limit-rate %(limit)sM"
    API_CMD = "curl -s -L -w '" + SPLIT_SIGN \
              + "%%{http_code}' -H 'IREPO-TOKEN:%(token)s' -H 'IREPO:irepo.py' '%(url)s'"
    URL_FORMAT = '%(host)s/rest/v2/%(module)s/%(resource_type)s/%(resource)s/%(target)s' \
                 '?m=%(module)s&path=%(path)s'

    @staticmethod
    def get_timeout():
        """
        get timeout seconds
        """
        return Constant.TIME_OUT_SECONDS

    @staticmethod
    def create_dir_if_need(path):
        """
        create dir of path if not exist
        """
        path_dir = os.path.dirname(path)
        if path != '' and path_dir != '' and not os.path.exists(path_dir):
            os.makedirs(path_dir)

    @staticmethod
    def exe_cmd(cmd_ctx, err_code=IRepoException.BAD_ARGS):
        """
        exe local command
        """
        print('[INFO] RUN [%s]' % cmd_ctx)
        st, out = subprocess.getstatusoutput(cmd_ctx)
        if st != 0:
            print("[ERROR] RUN [%s] failed!\n%s" % (cmd_ctx, out))
            raise IRepoException(err_code)


class ArgvParser(object):
    """
    argument parser
    """

    def __init__(self):
        usage = "irepo.py operation [options]"
        main_parser = argparse.ArgumentParser(
            prog="irepo.py", formatter_class=argparse.RawTextHelpFormatter, epilog="", usage=usage
        )
        main_parser.add_argument(
            "operation",
            help="set operation:\n<down> for download binary product"
        )
        main_parser.add_argument(
            "--uri", action="store", dest="uri", metavar='URI', default='',
            help="binary product URI:\n"
                 "irepo://${module}/releases/${fourBitVersion}?path=./output/xxx\n"
                 "irepo://${module}/nodes/${revision}?path=./output/xxx"
        )
        main_parser.add_argument(
            "--digest", "-d", action="store", dest="digest", metavar='DIGEST', default='',
            help="digest file of pre download, such as archer3' BACKUP_MD5_FILE"
        )
        main_parser.add_argument(
            "--mode", action="store", dest="mode", metavar='MODE', default='P2P',
            help="download mode, <P2P> or <HTTPS>, default <P2P>"
        )
        main_parser.add_argument(
            "--token", "-t", action="store", dest="token", metavar='AUTH_Token', default='',
            help="security auth token, apply from https://irepo.baidu-int.com/xxx/xx"
        )
        main_parser.add_argument(
            "--out", "-o", action="store", dest="output", metavar='pathName', default='',
            help="path name (with file name) to store. same as archer3' LOCAL_FILE\n"
                 "default: construct by fourBitVersion or revision in URI\n"
                 "irepo://baidu/agile/server/releases/1.0.1.1 => ./server_1.0.1.1.tar.gz\n"
        )
        main_parser.add_argument(
            "--tmp_digest", action="store", dest="tmp_digest", metavar='TMP_DIGEST_fileName',
            default='',
            help="tmp digest file name to store. same as archer3' TMP_REMOTE_MD5_FILE\n"
                 "default: construct by fourBitVersion or revision in URI\n"
                 "irepo://baidu/agile/server/releases/1.0.1.1 => ./server_1.0.1.1.tar.gz.digest\n"
        )
        main_parser.add_argument(
            "--tmp_out", action="store", dest="tmp_output", metavar='TMP_pathName', default='',
            help="tmp file name to store. same as archer3' TMP_LOCAL_FILE. default: same as --out"
        )
        main_parser.add_argument(
            "--full_path", action="store", dest="full_output", metavar='FULL_pathName', default='',
            help="full path name of deploy dir. same as archer3' FULL_DATA_PATH\n"
                 "default: same as full path of --out"
        )
        main_parser.add_argument(
            "--timeout", action="store", dest="timeout", metavar='TIME_OUT_SECONDS', default='600',
            help="operation max execute time, fail if timeout. default: 600s"
        )
        main_parser.add_argument(
            "--limit", action="store", dest="limit", metavar='UP/DOWN_LIMIT', default='50',
            help="up/down speed limit. default: 50M/S"
        )
        main_parser.add_argument(
            "--seed_type", action="store", dest="seed_type", metavar='SEED_TYPE', default='seed',
            help="gko3 down type, <uri> or <seed> default: <seed>"
        )
        main_parser.add_argument(
            "--source_type", action="store", dest="source_type", metavar='SOURCE_TYPE',
            default='tar', help="source file type, <tar> or <dir> default: <tar>"
        )
        self.options = main_parser.parse_args()

    def get_options(self):
        """
        get options
        """
        return self.options


class RepoClient(object):
    """
    client of irepo
    """

    def __init__(self, options=None):
        self.options = options

        self.module = ''
        self.four_bit_version = ''
        self.revision = ''
        self.path = ''
        self.output = ''
        # <releases> or <nodes>
        self.resource_type = 'releases'

        self.backup_digest = ''

        self.tmp_output = ''
        self.tmp_digest = ''
        self.full_output_path = ''
        self.timeout = 600
        self.limit = Constant.SPEED_LIMIT
        self.seed_type = 'seed'
        self.source_type = 'tar'

        self._parse_options()

        Constant.create_dir_if_need(self.output)
        Constant.create_dir_if_need(self.tmp_output)
        Constant.create_dir_if_need(self.backup_digest)
        Constant.create_dir_if_need(self.tmp_digest)

        Constant.TIME_OUT_SECONDS = self.timeout

        print('[INFO] options parse successfully.')

    def _parse_options(self):
        if self.options.operation != 'down':
            print('[ERROR] not support operation: [%s] !' % self.options.operation)
            raise IRepoException(IRepoException.BAD_ARGS)
        if str.strip(self.options.token) == '':
            print('[ERROR] empty token!')
            raise IRepoException(IRepoException.BAD_ARGS)
        uri = self.options.uri

        uri_matcher = re.search(Constant.PROTOCOL_REG, uri)
        if uri_matcher is None:
            print('[ERROR] bad uri!')
            raise IRepoException(IRepoException.BAD_ARGS)

        self.module = uri_matcher.group(1)
        self.resource_type = uri_matcher.group(2)
        self.resource = uri_matcher.group(3)
        if uri_matcher.group(4) is not None:
            self.path = uri_matcher.group(4)

        if str.strip(self.options.output) != '':
            self.output = str.strip(self.options.output)

        if self.path != '' and self.output == '':
            self.output = self.path

        if 'releases' == self.resource_type:
            self.four_bit_version = self.resource
            if self.output == '':
                self.output = '%s_%s.tar.gz' % (
                    self.module[self.module.rfind('/') + 1:], self.four_bit_version)
        else:
            self.revision = self.resource
            if self.output == '':
                self.output = '%s.tar.gz' % self.revision

        if str.strip(self.options.digest) != '':
            self.backup_digest = str.strip(self.options.digest)

        self.tmp_digest = str.strip(self.options.tmp_digest) \
            if str.strip(self.options.tmp_digest) != '' else self.output + '.digest'

        strip_tmp_out = str.strip(self.options.tmp_output)
        self.tmp_output = strip_tmp_out if strip_tmp_out != '' else self.output

        if str.strip(self.options.full_output) != '':
            self.full_output_path = str.strip(self.options.full_output)

        if self.full_output_path == '' or self.full_output_path.endswith('/'):
            self.full_output_path = self.full_output_path + self.output

        if int(self.options.timeout) > 0:
            self.timeout = int(self.options.timeout)

        if int(self.options.limit) > 0:
            self.limit = self.options.limit

        if str.strip(self.options.seed_type) != '':
            self.seed_type = self.options.seed_type

        if str.strip(self.options.source_type) != '':
            self.source_type = self.options.source_type

    def _invoke_request(self, target, params=None, request_type='API', retry=3):

        if params is None:
            params = {}
        api_info = {
            'module': self.module,
            'resource_type': self.resource_type,
            'resource': self.resource,
            'target': target,
            'path': self.path,
            'host': Constant.HOST
        }

        api_url = Constant.URL_FORMAT % api_info
        params['url'] = api_url
        params['token'] = self.options.token

        if 'API' == request_type:
            cmd_format = Constant.API_CMD
        elif 'FILE' == request_type:
            cmd_format = Constant.DOWNLOAD_CMD
        else:
            print('[ERROR] bad request type')
            raise IRepoException(IRepoException.BAD_ARGS)
        request_cmd = cmd_format % params

        try_count = 0
        while True:
            resp = ''
            http_code = '200'
            print('[INFO] CALL [%s]' % api_url)
            st, out = subprocess.getstatusoutput(request_cmd)
            if str.count(out, Constant.SPLIT_SIGN) > 0:
                resp, http_code = str.split(out, Constant.SPLIT_SIGN)
            if st != 0 or http_code != '200' or str.count(out, '<!DOCTYPE html>') > 0:
                if st != 0 and str.count(out, '<!DOCTYPE html>') < 1:
                    print("[ERROR] CALL [%s] failed! Cause by CURL cmd\n%s" % (api_url, out))
                else:
                    print("[ERROR] CALL [%s] failed! http_code [%s]" % (api_url, http_code))
                if try_count < retry:
                    try_count += 1
                    print('[INFO] retry %d' % try_count)
                else:
                    raise IRepoException(IRepoException.BAD_REQUEST)
            else:
                return resp
            time.sleep(2)

    def process_operation(self):
        """
        invoke cmd of client
        """
        if self.options.operation == 'down':
            try:
                self._download()
            except IRepoException as e_e:
                raise IRepoException(e_e.args[0])
            finally:
                print('[INFO] clean tmp files')
                if os.path.exists(self.tmp_digest):
                    os.remove(self.tmp_digest)
                if os.path.exists(self.tmp_output) and self.tmp_output != self.output:
                    os.remove(self.tmp_output)
            print('[INFO] download finish. file is %s' % self.output)

    @timeout(Constant.get_timeout)
    def _download(self):
        self._download_checksum()

        if not self._has_diff() and os.path.exists(self.full_output_path):
            print('[INFO] target is the same as download before. need not redownload it.')
            exit(0)

        if self.options.mode == 'HTTPS':
            print('[INFO] start https download')
            self._invoke_request('files', {'out': self.tmp_output, 'limit': self.limit}, 'FILE')
        else:
            print('[INFO] start P2P download')
            self._p2p_download()

        self._check_sum()

        if self.tmp_output != self.output:
            Constant.exe_cmd('mv %s %s' % (self.tmp_output, self.output), IRepoException.MV_FAILED)
        if self.backup_digest != '':
            Constant.exe_cmd('mv %s %s' % (self.tmp_digest, self.backup_digest),
                             IRepoException.MV_FAILED)

    def _download_checksum(self):
        print('[INFO] start download md5')
        if 'tar' == self.source_type:
            md5 = self._invoke_request('files/md5')
            if md5 == '':
                print('[ERROR] download remote checksum failed! please check download uri')
                raise IRepoException(IRepoException.BAD_ARGS)
            out_name = os.path.basename(self.tmp_output)
            Constant.exe_cmd('echo "%s  %s" >%s' % (md5, out_name, self.tmp_digest),
                             IRepoException.CHECK_FAILED)
        elif 'dir' == self.source_type:
            entry_resp = self._invoke_request('entries')
            if entry_resp == '':
                print('[ERROR] download remote checksum failed! please check download uri')
                raise IRepoException(IRepoException.BAD_ARGS)
            digest_list = json.loads(entry_resp)
            md5list = []
            for entry in digest_list:
                md5list.append(entry['md5'] + '  ' + entry['path'])
            Constant.exe_cmd('echo %s\n >%s' % ('\n'.join(md5list), self.tmp_digest),
                             IRepoException.CHECK_FAILED)

    def _has_diff(self):
        if self.backup_digest is '' or self.tmp_digest is '':
            return True
        digest_check_cmd = 'diff %s %s' % (self.backup_digest, self.tmp_digest)
        print('[INFO] RUN [%s]' % digest_check_cmd)
        st, out = subprocess.getstatusoutput(digest_check_cmd)
        print('[INFO] md5 diff %s' % out)
        return st != 0

    def _p2p_download(self):
        seed_info = None
        seed_status = 'RUNNING'
        sleep_time = 0
        tried_count = 0
        print('[INFO] start make seed.')
        while seed_status == 'RUNNING' and tried_count < 3:
            wait_time = sleep_time + random.randint(0, 5)
            print('[INFO] seeding... wait %s seconds' % str(wait_time))
            time.sleep(wait_time)
            resp = self._invoke_request('seeds', {'seed_type': self.seed_type,
                                                  'source_type': self.source_type})
            seed_info = json.loads(resp)
            if seed_info is not None:
                seed_status = seed_info['status']
                if sleep_time > 5:
                    sleep_time = sleep_time / 10
                else:
                    sleep_time = 50 * int(seed_info['fileBytes']) / (1024 * 1024 * 1024) + 10
            tried_count += 1

        if seed_info is None or seed_info['status'] != 'SUCC':
            print('[ERROR] make seed failed!')
            raise IRepoException(IRepoException.SEED_FAILED)
        gko3_cmd = ['gko3', 'down', '-d', self.limit, '-u', self.limit, '-n', self.tmp_output]
        seed_hash = seed_info['infoHash']
        seed_cluster = seed_info['cluster']

        if 'seed' == self.seed_type and seed_hash is not None and seed_hash != '':
            gko3_cmd.append('-i')
            gko3_cmd.append(seed_hash)
        elif 'uri' == self.seed_type and seed_cluster is not None and seed_cluster != '':
            gko3_cmd.append('-C')
            gko3_cmd.append(seed_cluster)
        else:
            raise IRepoException(IRepoException.SEED_FAILED)

        print('[INFO] start gko3 download. seed is %s' % seed_hash)
        process = subprocess.Popen(gko3_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        pat_log = re.compile('progress:\s*([^ ,]+),.*downrate:\s*([^ ,]+)')
        pat_fatal = re.compile('^\[FATAL\](.*)')
        while process.poll() is None:
            proc_output = process.stderr.readline()
            m = pat_log.search(proc_output)
            if m is not None:
                print("[INFO] progress: %s, speed: %s" % (m.group(1), m.group(2)))
            else:
                m = pat_fatal.search(proc_output)
                if m is not None:
                    print("[ERROR] %s" % m.group(1))
        proc_ret = process.wait()
        if proc_ret != 0:
            print('[ERROR] P2P download failed!')
            raise IRepoException(IRepoException.GKO3_FAILED)

    def _check_sum(self):
        print('[INFO] start check md5')
        tmp_out_dir = os.path.dirname(self.tmp_output)
        pwd = os.getcwd() + '/'
        if str.strip(tmp_out_dir) == '':
            tmp_out_dir = './'
        abs_path = self.tmp_digest if self.tmp_digest.startswith('/') else pwd + self.tmp_digest
        c_cmd = 'cd %s && md5sum -c %s && cd %s' % (tmp_out_dir, abs_path, pwd)
        Constant.exe_cmd(c_cmd, IRepoException.CHECK_FAILED)


if __name__ == '__main__':
    try:
        RepoClient(ArgvParser().get_options()).process_operation()
    except IRepoException as e:
        print('[ERROR] download failed!')
        exit(e.args[0])
    except Exception as ee:
        print('[FATAL] %s' % ee.message)
        exit(IRepoException.UNKNOWN)
