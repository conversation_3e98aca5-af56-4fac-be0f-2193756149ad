#!/usr/bin/env bash
# 一键下载模型,适合打包在部署镜像中（专为Aiflow提供））
# 依赖python, nc, curl, gko3, sha256sum, hostname, uuidgen 以及基础命令 rm, wc, kill, mkdir 等

token=""
revision=""
timeout=600
out=""

mkdir -p "/tmp/.irepo"
# 运行时随机生成
LOCAL_PORT=""

PROTOCOL="${PROTOCOL:-http}"
IREPO_HOST="${IREPO_HOST:-irepotest.baidu-int.com}"

# detect path of sha256sum command
sha256sum --help > /dev/null 2>&1
if [ $? -ne 0 ]; then
    /opt/compiler/gcc-4.8.2/bin/sha256sum --help > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "sha256sum command not found on this host"
        exit 1
    else
        SHA256SUM=/opt/compiler/gcc-4.8.2/bin/sha256sum
    fi
else
    SHA256SUM=sha256sum
fi
echo "detected sha256sum command on this host: $SHA256SUM"

# detect if openssl supports sha256 digest algorithm, thus we will ignore
# certificate validation errors when accessing irepo servers through https
CURL_OPTS=""
if [ "$PROTOCOL" = "https" ]; then
    /usr/bin/openssl dgst -help 2>&1  | grep sha256 > /dev/null
    if [ $? -ne 0 ]; then
        CURL_OPTS="-k"
        echo "WARNING: openssl on this host does not support sha256 digest algorithm, "
        echo "  so we will ignore SSL certificate errors when accessing irepo through https."
        echo "  This will expose you to risks such as phishing and man-in-the-middle attack."
    fi
fi

RESPONSE="HTTP/1.1 200 OK\r\nConnection: keep-alive\r\n\r\nOK\r\n"
SEED_FILE="/tmp/.irepo/seed_info.irepo_$(uuidgen)"
SHA256_FILE="/tmp/.irepo/irepo.file.sha256_$(uuidgen)"
SEED_INFO_HASH="NONE"

trap stop_listener INT

stop_listener()
{
    curl -L "http://localhost:${LOCAL_PORT}/NONE" &> /dev/null
}
listen()
{   echo "start wait callback, listen on ${LOCAL_PORT}"
    # check if we need `-p` option
    local nc_opt="-l"
    local nc_need_p_opt=$(nc -h 2>&1 | grep "^\[v")
    if [ -n "$nc_need_p_opt" ]; then
        nc_opt="-lp"
    fi
    # 种子回调地址格式 http://host:port/seedinfohash
    { echo -en "${RESPONSE}"; } | nc $nc_opt "${LOCAL_PORT}" | \
        sed -n 1p | awk '{print $2}' | awk -F "/" '{print $2}' > ${SEED_FILE}
    return $?
}
load_seed_hash()
{
    SEED_INFO_HASH="$(cat ${SEED_FILE} && rm -rf ${SEED_FILE})"
    code=$?
    if [[ "${code}" != "0" ]]; then
        echo "load seed info failed!"
        exit ${code}
    fi
}
function timeout()
{
    local ti cmd pid

    if echo "$1" | grep -Eq '^[0-9]+'; then
        ti=$1
        shift && cmd="$@"
    else
        ti=600
        cmd="$@"
    fi
    ${cmd} &
    pid=$!

    while kill -0 ${pid} &>/dev/null; do
        sleep 1
        let ti-=1

        if [ "$ti" = "0" ]; then
            stop_listener
            wait ${pid} &>/dev/null
        fi
    done
}

_config_random_local_port()
{
    local local_port=$((RANDOM%10000+10000))
    local p_id=$(/usr/sbin/lsof -i :${local_port}|grep -v "PID" | awk '{print $2}')
    local i=1

    while [[ "${p_id}" != "" ]] && [[ i -gt 10 ]];
    do
        local_port=$((RANDOM%10000+10000+${i}))
        p_id=$(/usr/sbin/lsof -i :8081|grep -v "PID" | awk '{print $2}')
        i=+1
    done
    if [[ "${p_id}" != "" ]];
    then
       echo "fail to find local port!"
       exit 1
    else
       LOCAL_PORT=${local_port}
    fi
}
make_seed()
{
    echo "start make seed ...."
    local_ip=$(hostname -i)
    _config_random_local_port
    MK_GKO3_SEED_URL="${PROTOCOL}://${token}@${IREPO_HOST}/rest/v1/nodes/${revision}/seeds"
    SEED_INFO_HASH=$(curl $CURL_OPTS -L -H'Content-Type:application/json' "${MK_GKO3_SEED_URL}" \
                          -d"{\"callback\":\"http://${local_ip}:${LOCAL_PORT}\"}" 2>/dev/null)

    code=$?
    if [[ "${code}" != "0" ]]; then
        echo "make seed failed! error: [connect irepo failed!]"
        exit ${code}
    fi
    # 种子infohash理论上不会包含"(引号)
    if [[ ${SEED_INFO_HASH} =~ "\"" ]]; then
        echo "make seed failed! error: [${SEED_INFO_HASH}]"
        exit 1
    fi
}
p2p_download()
{
    echo "start gko3 job ...."
    gko3 down -d 100 -S 0 -u 50 --auto-add-time 3600 -i ${SEED_INFO_HASH} -n ${out} &>/dev/null
    code=$?
    if [[ "${code}" != "0" ]]; then
        echo "gko3 download seed [${SEED_INFO_HASH}] failed!"
        exit ${code}
    fi
}
check_sum()
{
    echo "start check sha256 ...."
    SHA256_URL="${PROTOCOL}://${token}@${IREPO_HOST}/rest/v1/nodes/${revision}/files/sha256"
    sha256="$(curl $CURL_OPTS -L -H'Content-Type:application/json' "${SHA256_URL}" 2>/dev/null)"
    echo "${sha256}  ${out}" > ${SHA256_FILE}
    if [[ ! -n "$($SHA256SUM -c ${SHA256_FILE} | grep "${out}" | grep "OK" && rm -rf ${SHA256_FILE})" ]]; then
        echo "check sum failed!"
        exit 1
    fi
}

while getopts t:s:m:r:o:w: opt
do
    case ${opt} in
        t) token=${OPTARG} ;;
        r) revision=${OPTARG} ;;
        o) out=${OPTARG} ;;
        w) timeout=${OPTARG} ;;
        ?) help_info
           exit 1 ;;
    esac
done

if [ -z "$out" ]; then
    out="${revision}.tar"
fi
echo "start download mode [irepo://${revision}] to [${out}]"

make_seed

if [[ "${SEED_INFO_HASH}" == "NONE" ]]; then
    timeout ${timeout} listen

    load_seed_hash
fi

p2p_download

check_sum

echo "finish"
