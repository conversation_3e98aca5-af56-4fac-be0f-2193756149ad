# -* encoding:utf-8 -*-
"""
repocli adapter
"""
import os
import sys

from common import constant
from buildin.cli.console import Log
from common.conf import Config


def get_adapted_args():
    """
    获取被适配的命令
    """
    args = sys.argv
    Log.debug(args)
    return __get_adapter(args)


class BaseCommandAdapter(object):
    """
    命名适配器基类
    """

    @classmethod
    def can_adapt(cls, args):
        """
        是否可以适配
        """
        return False

    @classmethod
    def convert(cls, args):
        """
        转换命令
        """
        return None


class L1CommandAdapter(BaseCommandAdapter):
    """
    L1命令适配器基类
    """

    @classmethod
    def can_adapt(cls, args):
        """
        can_adapt
        """
        if len(args) <= 1:
            return False
        return args[1] in ('prod', 'version')

    @classmethod
    def convert(cls, args):
        """
        convert
        """
        args = args[1:]
        args.append('--server')
        args.append(constant.HOST)
        return args


class L1ModelCommandAdapter(L1CommandAdapter):
    """
    L1 model命令适配器
    """

    @classmethod
    def can_adapt(cls, args):
        """
        can_adapt
        """
        if len(args) <= 1:
            return False
        return args[1] == 'model'

    @classmethod
    def convert(cls, args):
        """
        convert
        """
        args = super(L1ModelCommandAdapter, cls).convert(args)
        userdata_conf_path = os.path.join(sys.path[0], Config.USER_DATA_CONF_NAME)
        if not os.path.isfile(userdata_conf_path):
            return args
        args.append('--conf')
        args.append(sys.path[0])
        return args


ADAPTERS = [L1CommandAdapter, L1ModelCommandAdapter]


def __get_adapter(args):
    for adapter in ADAPTERS:
        if adapter.can_adapt(args):
            return adapter.convert(args)
    return None
