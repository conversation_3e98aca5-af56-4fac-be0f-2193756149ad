# coding=utf-8
"""
repocli-golang 下载
"""
import os
import stat
import sys
import platform

import requests

from buildin.cli import request_tools, local_tools
from buildin.cli.console import Log
from common import constant

REPOCLI_GOLANG_RELATIVE_PATH = "bin/repocli"


def download_if_necessary():
    """
    如果没有，则下载
    """
    repocli_golang_abs_path = os.path.join(sys.path[0], REPOCLI_GOLANG_RELATIVE_PATH)
    if os.path.isfile(repocli_golang_abs_path):
        return repocli_golang_abs_path
    local_tools.create_path_dir(repocli_golang_abs_path)
    os_name = platform.system().lower()
    Log.debug('os: %s' % os_name)
    arch_name = "amd64"
    download_url = 'http://' + constant.HOST + constant.REPOCLI_GOLANG_DOWN_URL % (os_name, arch_name)
    Log.info('repocli-golang_download_url: %s' % download_url)
    response = requests.get(download_url, verify=False, stream=True)
    if response.status_code >= 400:
        Log.error("request download url(%s) error: %s" % (download_url, response.content))
        return None
    request_tools.write_to_file(response, repocli_golang_abs_path)
    os.chmod(repocli_golang_abs_path, stat.S_IXOTH | stat.S_IROTH | stat.S_IXGRP | stat.S_IRWXU)
    return repocli_golang_abs_path
