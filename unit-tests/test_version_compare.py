# -*- coding:utf-8 -*-
"""
版本号测试
"""
from unittest import TestCase

from common.version_utils import compare
from common.version_utils import is_four_bit_version
from common.version_utils import is_three_bit_version


class TestVersionUtils(TestCase):
    """
    TestVersionUtils
    """

    def test_compare(self):
        """
        test_compare
        """
        self.assertEqual(compare("1.9.1", "1.8.3"), 1)
        self.assertEqual(compare("1.9.1", "1.9"), 1)
        self.assertEqual(compare("1.9.2", "1.9"), 1)
        self.assertEqual(compare("1.9", "1.9"), 0)
        self.assertEqual(compare("7.5.2.4", "7.5.3"), -1)

    def test_three_bit_version(self):
        """
        test_three_bit_version
        """
        self.assertFalse(is_three_bit_version("1.0.0.1"))
        self.assertFalse(is_three_bit_version(""))
        self.assertTrue(is_three_bit_version("1.0.0"))
        self.assertFalse(is_three_bit_version("1.0."))
        self.assertFalse(is_three_bit_version("1.0.fdghjkl"))

    def test_four_bit_version(self):
        """
        test_four_bit_version
        """
        self.assertFalse(is_four_bit_version("1.0.0"))
        self.assertFalse(is_four_bit_version(""))
        self.assertTrue(is_four_bit_version("1.0.0.1"))
        self.assertFalse(is_four_bit_version("1.0.0."))
        self.assertFalse(is_four_bit_version("1.0.0.asdds"))
