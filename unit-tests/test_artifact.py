""" artifact.py test """
import json
from collections import namedtuple
from unittest import TestCase

from mock import patch

from buildin.cli import cachemate
from buildin.cli.artifact import Artifact
from buildin.cli.exception import IRepoException


class TestBuildArtifact(TestCase):
    """ Test BuildArtifact """

    def test_artifact_create(self):
        """ create artifact """
        repo_type = 'bundle'
        repo_name = 'baidu/agile/test'
        revision_version = '1' * 32
        revision_arti = Artifact.create_artifact(repo_type, repo_name, revision_version)
        self.assertEqual(revision_arti.__class__.__name__, 'RemoteBuildArtifact')

        current_build = 'current_build@'
        current_arti = Artifact.create_artifact(repo_type, repo_name, current_build)
        self.assertEqual(current_arti.__class__.__name__, 'LocalArtifact')

    def test_artifact_method(self):
        """ artifact normal method """
        # for mock
        cachemate.CacheMeta.CACHE_HOME = './tmp'
        repo_type = 'bundle'
        repo_name = 'baidu/irepo/test'
        release_version = '3e450e9be530489c8b372270d6e1227b'
        arti = Artifact.create_artifact(repo_type, repo_name, release_version)
        arti2 = Artifact.create_artifact(repo_type, repo_name, release_version)
        self.assertEqual(arti, arti2)
        self.assertEqual(arti, arti)
        self.assertEqual('./tmp/bundle/baidu/irepo/test/nodes/3e450e9be530489c8b372270d6e1227b',
                         arti.get_cache_root())
        self.assertEqual('baidu/irepo/test@3e450e9be530489c8b372270d6e1227b', arti.readable())
        self.assertEqual(
            './tmp/bundle/baidu/irepo/test/nodes/3e450e9be530489c8b372270d6e1227b/files',
            arti.get_files_dir())
        self.assertEqual(4, len(arti.get_attribute_list()))
        self.assertEqual('nodes', arti.get_attribute_list()[2])
        self.assertEqual('3e450e9be530489c8b372270d6e1227b', arti.get_attribute_list()[3])

    def test_artifact_create_by_resource(self):
        """ create_by_resource """
        repo_type = 'bundle'
        repo_name = 'baidu/agile/test'
        resource_type = 'nodes'
        resource = '3e450e9be530489c8b372270d6e1227b'
        revision_arti = Artifact.create_artifact_by_resource(repo_type, repo_name, resource_type,
                                                             resource)
        self.assertEqual(revision_arti.__class__.__name__, 'RemoteBuildArtifact')

    def test_artifact_create_by_resource_error(self):
        """ create_by_resource exception """
        repo_type = 'bundle'
        repo_name = 'baidu/agile/test'
        with self.assertRaises(IRepoException):
            Artifact.create_artifact_by_resource(repo_type, repo_name, 'nodes', '1111')
        with self.assertRaises(IRepoException):
            Artifact.create_artifact_by_resource(repo_type, repo_name, 'releases', '1.0.1')

    def test_artifact_create_failed(self):
        """ artifact create failed """
        with self.assertRaises(IRepoException):
            repo_type = 'bundle'
            repo_name = 'baidu/agile/test'
            release_version = '1.0.0.1'
            Artifact.create_artifact(repo_type, repo_name, release_version)

    def test_artifact_equals_others(self):
        """ artifact equals """
        repo_type = 'bundle'
        repo_name = 'baidu/agile/test'
        release_version = '3e450e9be530489c8b372270d6e1227b'
        arti = Artifact.create_artifact(repo_type, repo_name, release_version)
        self.assertFalse(arti is None)
        self.assertFalse(arti == 1)

    @patch('buildin.cli.request_tools.check_response')
    @patch('buildin.cli.request_tools.do_get')
    def test_create_release_artifact_has_rollback(self, do_get, check_response):
        """ artifact has rollback """
        Response = namedtuple('Response', ['code', 'content'])

        do_get.return_value = Response(code=200, content=json.dumps(
            {'revision': '6e297ab706d94dd8a795ba71cb6bf1aa',
             'roll_back': True}))
        check_response.return_value = None
        repo_type = 'bundle'
        repo_name = 'baidu/agile/test'
        release_version = '1.0.0.1@release'
        try:
            Artifact.create_artifact(repo_type, repo_name, release_version)
        except IRepoException as e:
            self.assertTrue('rollback' in e.message)
            return
        self.fail()
