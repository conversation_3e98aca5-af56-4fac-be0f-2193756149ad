# -*- coding:utf-8 -*-
""" 测试缓存meta信息 """
import os
from unittest import TestCase

from buildin.cli.artifact import CacheMeta
from buildin.cli.artifact import CacheStatus
from buildin.cli.archive import TargzArchive


class TestCacheMeta(TestCase):
    """ Test CacheMeta """
    file_name = './meta'

    def test_cache_meta(self):
        """ 测试基本操作 """
        meta = CacheMeta(TestCacheMeta.file_name)
        self.assertEqual(meta.get_status(), CacheStatus.NONE)
        meta.init_meta()
        self.assertEqual(meta.get_status(), CacheStatus.INITED)
        self.assertEqual(meta.get_file_name(), '.')
        self.assertEqual(meta.get_archive_type(), '')
        meta.set_file_name('realfilename')
        self.assertEqual(meta.get_file_name(), 'realfilename')
        meta.set_status(CacheStatus.FINISHED)
        self.assertEqual(meta.get_status(), CacheStatus.FINISHED)
        meta.set_archive_type(TargzArchive.suffix)
        self.assertEqual(meta.get_archive_type(), TargzArchive.suffix)

    def tearDown(self):
        """ 后处理 """
        os.remove(TestCacheMeta.file_name)
