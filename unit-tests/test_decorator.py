# -*- coding:utf-8 -*-
""" 测试装饰器 """
import time
import unittest

from buildin.cli.decorator import timeout
from buildin.cli.exception import IRepoTimeoutException


class TestDecorator(unittest.TestCase):
    """ 测试装饰器 """

    def test_time_out(self):
        """ 测试超时 """

        def _get_time():
            return 1

        @timeout(_get_time)
        def _timeout_method():
            time.sleep(2)

        with self.assertRaises(IRepoTimeoutException):
            _timeout_method()
