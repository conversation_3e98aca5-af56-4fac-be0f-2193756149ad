# -* encoding:utf-8 -*-
""" local_tools test """
import os
from unittest import TestCase

from mock import patch

from buildin.cli import local_tools
from buildin.cli.exception import IRepoException


class TestLocalTools(TestCase):
    """ 测试 本地工具 """

    def test_remove(self):
        """ test remove """
        d = ".temp"
        local_tools.exe_cmd("mkdir %s" % d)
        local_tools.remove(d)
        self.assertFalse(os.path.exists(d))

        f = ".tempfile"
        local_tools.exe_cmd("touch %s" % f)
        local_tools.remove(f)
        self.assertFalse(os.path.exists(f))

    def test_get_sub_dirs_none(self):
        """ 测试 获取子目录 """
        self.assertEqual(0, len(local_tools.get_sub_dirs("not-exist-dir")))

    def test_get_sub_dirs(self):
        """ 测试 获取子目录 """
        root_dir = ".ws"
        local_tools.exe_cmd("mkdir -p %s" % os.path.join(root_dir, "dir1"))
        local_tools.exe_cmd("mkdir -p %s" % os.path.join(root_dir, "dir2"))
        local_tools.exe_cmd("mkdir -p %s" % os.path.join(root_dir, "dir3"))
        local_tools.exe_cmd("mkdir -p %s" % os.path.join(root_dir, "dir1/file1"))
        local_tools.exe_cmd("mkdir -p %s" % os.path.join(root_dir, "dir1/file2"))
        local_tools.exe_cmd("mkdir -p %s" % os.path.join(root_dir, "dir1/dir3"))
        sub_dirs = local_tools.get_sub_dirs(root_dir, 1)
        self.assertTrue('.ws/dir2' in sub_dirs)
        self.assertTrue('.ws/dir3' in sub_dirs)
        self.assertTrue('.ws/dir1' in sub_dirs)
        sub_dirs2 = local_tools.get_sub_dirs(root_dir, 2)
        self.assertTrue('.ws/dir1/file2' in sub_dirs2)
        self.assertTrue('.ws/dir1/dir3' in sub_dirs2)
        self.assertTrue('.ws/dir1/file1' in sub_dirs2)
        local_tools.remove(root_dir)

    def test_is_dir_path(self):
        """ 测试 dir判断 """
        self.assertFalse(local_tools.is_dir_path("file"))
        self.assertFalse(local_tools.is_dir_path(None))
        self.assertTrue(local_tools.is_dir_path("dir/"))

    def test_is_file_path(self):
        """ 测试 file判断 """
        self.assertTrue(local_tools.is_file_path("file"))
        self.assertFalse(local_tools.is_file_path("dir/"))
        self.assertFalse(local_tools.is_file_path(None))

    def test_create_path_dir(self):
        """ 测试 为路径创建目录 """
        root_dir = ".ws2"
        local_tools.create_path_dir(os.path.join(root_dir, "dir/file"))
        self.assertTrue(os.path.isdir(".ws2/dir"))

        local_tools.create_path_dir(os.path.join(root_dir, "dir/dir2/"))
        self.assertTrue(os.path.isdir(".ws2/dir/dir2"))
        local_tools.remove(root_dir)

    def test_exec_cmd(self):
        """ 测试执行本地命令 """
        with self.assertRaises(IRepoException):
            local_tools.exe_cmd("command not exits")

    def test_ws(self):
        """ 测试workspace """
        local_tools.clean_tmp_workspace()
        local_tools.init_temp_workspace()
        self.assertTrue(os.path.isdir(local_tools.TEMP_WORKSPACE_ROOT))
        local_tools.clean_tmp_workspace()
        self.assertFalse(os.path.isdir(local_tools.TEMP_WORKSPACE_ROOT))

    def test_generate_artifact_unique_temp_name(self):
        """ 测试获取本地随机临时文件名字"""
        temp_name = local_tools.generate_artifact_unique_temp_name('bundle',
                                                                   'baidu/irepo-test/bundle',
                                                                   '1.0.0.1')
        self.assertEqual(local_tools.TEMP_WORKSPACE_ROOT, os.path.dirname(temp_name))

    def test_command_exist(self):
        """ 测试本地命令是否存在 """
        self.assertTrue(local_tools.command_exist("cp"))
        self.assertFalse(local_tools.command_exist("command_not_found"))

    @patch("os.path.isdir")
    def test_abs_command_exist(self, is_dir):
        """ 测试绝对路径的本地命令是否存在 """
        local_tools.command_exist("/a/b/c/d")
        is_dir.assert_not_called()

    @patch('os.path.isdir')
    @patch('subprocess.getstatusoutput')
    def test_get_git_repo_name_https(self, getstatusoutput, is_dir):
        is_dir.return_value = True
        getstatusoutput.return_value = 0, 'origin	https://<EMAIL>/baidu/agile/plugin-poll (fetch)'
        self.assertEqual('baidu/agile/plugin-poll', local_tools.get_git_repo_name('./'))

    @patch('os.path.isdir')
    @patch('subprocess.getstatusoutput')
    def test_get_git_repo_name_ssh(self, getstatusoutput, is_dir):
        is_dir.return_value = True
        getstatusoutput.return_value = 0, 'origin  ssh://<EMAIL>:8235/baidu/artifact/client (push)'
        self.assertEqual('baidu/artifact/client', local_tools.get_git_repo_name('./'))
