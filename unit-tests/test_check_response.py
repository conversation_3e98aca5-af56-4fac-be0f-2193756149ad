# -* encoding:utf-8 -*-
""" 测试request_tools """
import collections
import json
from unittest import TestCase

from buildin.cli import request_tools
from buildin.cli.exception import IRepoException


class TestRequestTools(TestCase):
    """ 测试request_tools """
    Response = collections.namedtuple('Response', ['status_code', 'content'])

    def test_check_response_200(self):
        """ check 200 """
        response = TestRequestTools.Response(200, '')
        request_tools.check_response(response)

        response = TestRequestTools.Response(201, '')
        request_tools.check_response(response)

    def test_check_response_403(self):
        """ check 403 """
        response = TestRequestTools.Response(403, json.dumps({'msg': 'forbidden', 'code': 403}))
        try:
            request_tools.check_response(response, 'bundle', 'baidu/irepo/test', '1.0.0.1')
        except IRepoException as ie:
            self.assertEqual("current user not have [bundle-baidu/irepo/test-1.0.0.1] permission",
                             ie.message)
        try:
            request_tools.check_response(response, *['bundle', 'baidu/irepo/test', '1.0.0.1'])
        except IRepoException as ie:
            self.assertEqual("current user not have [bundle-baidu/irepo/test-1.0.0.1] permission",
                             ie.message)

    def test_check_response_404(self):
        """ check 404 """
        response = TestRequestTools.Response(404, json.dumps({'msg': 'not found', 'code': 404}))
        try:
            request_tools.check_response(response, 'bundle', 'baidu/irepo/test', '1.0.0.1')
        except IRepoException as ie:
            self.assertEqual("not found [bundle-baidu/irepo/test-1.0.0.1]", ie.message)

    def test_check_response_400(self):
        """ check 404 """
        response = TestRequestTools.Response(400, json.dumps({'msg': 'not ok', 'code': 404}))
        try:
            request_tools.check_response(response, 'bundle', 'baidu/irepo/test', '1.0.0.1')
        except IRepoException as ie:
            self.assertEqual("400 not ok", ie.message)

    def test_check_response_502_html(self):
        """ check 502 """
        response = TestRequestTools.Response(502, "<html>502</html>")
        try:
            request_tools.check_response(response, 'bundle', 'baidu/irepo/test', '1.0.0.1')
        except IRepoException as ie:
            self.assertEqual("502 unknown error", ie.message)

    def test_check_response_407(self):
        """ check unknown """
        response = TestRequestTools.Response(407, json.dumps({'msg': 'not ok', 'code': 403}))
        try:
            request_tools.check_response(response, 'bundle', 'baidu/irepo/test', '1.0.0.1')
        except IRepoException as ie:
            self.assertEqual("407 not ok", ie.message)

    def test_check_response_args(self):
        """ check unknown """
        response = TestRequestTools.Response(404, json.dumps({'msg': 'not ok', 'code': 404}))
        try:
            request_tools.check_response(response, 1, 2, 3, 4, 5)
        except IRepoException as ie:
            self.assertEqual("not found [1-2-3-4-5]", ie.message)
