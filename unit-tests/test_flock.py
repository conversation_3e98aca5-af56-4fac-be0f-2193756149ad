# -* encoding:utf-8 -*-
""" 文件锁测试 """
import os
from unittest import TestCase

from buildin.cli.filelock import FileLock


class TestFLock(TestCase):
    """ 测试文件锁 """
    lock_file = 'STATUS'

    def test_flock(self):
        """ 获取与释放 """
        lock = FileLock(TestFLock.lock_file)
        try:
            lock.acquire()
            f = open(TestFLock.lock_file, 'w')
            f.write('RUNNING')
        finally:
            f.close()
            lock.release()

    def tearDown(self):
        """ 后处理 """
        os.remove(TestFLock.lock_file)
