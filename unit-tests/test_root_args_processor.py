# -* encoding:utf-8 -*-
""" RootArgsProcessor测试 """
from unittest import TestCase

from mock import patch

from buildin.cli.options import KeySafeDict, GlobalOptions
from buildin.executors.abstract import RootArgsProcessor


class TestRootArgsProcessor(TestCase):
    """ Test RootArgsProcessor """

    def test_process_name_releases(self):
        """ test process name releases options """
        options = KeySafeDict({'name': 'baidu/artifact/test:1.0.0.1'})
        root = RootArgsProcessor(options=options)
        root.process_rec()
        self.assertEqual(root.options['module'], 'baidu/artifact/test')
        self.assertEqual(root.options['resource'], '1.0.0.1')
        self.assertEqual(root.options['resource_type'], 'releases')

    def test_process_name_nodes(self):
        """ test process name nodes options """
        options = KeySafeDict({'name': 'baidu/artifact/test@1234567890'})
        root = RootArgsProcessor(options=options)
        root.process_rec()
        self.assertEqual(root.options['module'], 'baidu/artifact/test')
        self.assertEqual(root.options['resource'], '1234567890')
        self.assertEqual(root.options['resource_type'], 'nodes')

    def test_process_name_module(self):
        """ test process name module options """
        options = KeySafeDict({'name': 'baidu/artifact/test'})
        root = RootArgsProcessor(options=options)
        root.process_rec()
        self.assertEqual(root.options['module'], 'baidu/artifact/test')

    @patch('buildin.cli.local_tools.get_git_repo_name')
    def test_process_name_no_module(self, get_git_repo_name):
        """ test process name no name options """
        module = 'baidu/artifact/test'
        get_git_repo_name.return_value = module
        root = RootArgsProcessor(options=KeySafeDict())
        root.process_rec()
        self.assertEqual(root.options['module'], module)

    def test_process_user_token(self):
        """ test process user token """
        options = KeySafeDict(
            {'name': 'a/b/c',
             'user': 'xujinfeng',
             'token': 'token',
             'disable_giano': False
             })
        root = RootArgsProcessor(options=options)
        root.process_rec()
        self.assertEqual(GlobalOptions.user, 'xujinfeng')
        self.assertEqual(GlobalOptions.token, 'token')