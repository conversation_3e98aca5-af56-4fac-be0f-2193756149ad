""" argparse frame test"""
import unittest
import argparse

from buildin.providers import abstract


class TestArgParseFrame(unittest.TestCase):
    """ ArgParser Test """
    def test_arg_ddder(self):
        """ test arg_ddder """

        # _TestArgAdder class
        class _TestArgAdder(abstract.RootArgAdder):
            def add_arg(self, parser):
                """ add arg """
                parser.add_argument(
                    "--test", action="store", dest="test", metavar='TEST_NAME', default='',
                    required=True,
                    help="not help you"
                )

        parser = argparse.ArgumentParser()
        _TestArgAdder().assemble(parser)
        options = parser.parse_args("--test test --timeout 1000".split())
        self.assertEqual(options.test, 'test')
        self.assertEqual(options.timeout, 1000)
        self.assertEqual(options.debug, False)