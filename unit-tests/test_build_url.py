# -* encoding:utf-8 -*-
""" 测试 request_tools build_url """
from unittest import TestCase

from buildin.cli import request_tools


class TestBuildUrl(TestCase):
    """ 测试 request_tools build_url """

    def test_normal_build(self):
        """ test_normal_build """
        url = request_tools.build_url('model', 'baidu/irepo-test/bundle', 'releases', '1.0.0.1')
        self.assertEqual("http://irepotest.baidu-int.com/rest/model/v3/baidu/irepo-test/bundle"
                         "/releases/1.0.0.1", url)

    def test_normal_build_invalid_repo_type(self):
        """ test_normal_build_invalid_repo_type """
        with self.assertRaises(AssertionError):
            request_tools.build_url('models', 'baidu/irepo-test/bundle', 'releases', '1.0.0.1')

    def test_normal_build_invalid_length(self):
        """ test_normal_build_invalid_length """
        with self.assertRaises(AssertionError):
            request_tools.build_url('models')
