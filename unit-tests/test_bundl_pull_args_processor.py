# -*- coding:utf-8 -*-
""" bundle pull arg processor test"""
from unittest import TestCase

from buildin.cli.options import KeySafeDict
from buildin.executors.bundle_pull import BundlePullArgsProcessor


class TestBundlePullArgsProcessor(TestCase):
    """ Test BundlePullArgsProcessor"""

    def test_process_default(self):
        """ 默认场景"""
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('baidu_irepo-test_bundle_1-0-0-1_bundle', options['out_dir'])
        self.assertEqual('', options['out_file_name'])

    def test_process_default_nodes(self):
        """ 默认场景"""
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle@e68e07d913d74a8a9d39dd5c6aedbd53'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('e68e07d913d74a8a9d39dd5c6aedbd53', options['resource'])
        self.assertEqual('nodes', options['resource_type'])
        self.assertEqual('baidu_irepo-test_bundle_e68e07d_bundle', options['out_dir'])
        self.assertEqual('', options['out_file_name'])

    def test_process_default_compress(self):
        """ -c """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('./', options['out_dir'])
        self.assertEqual('baidu_irepo-test_bundle_1-0-0-1_bundle.tar.gz', options['out_file_name'])

    def test_process_default_to_dir(self):
        """ dir/ """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'out': 'dir/'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('dir/', options['out_dir'])
        self.assertEqual('', options['out_file_name'])

    def test_process_default_to_file(self):
        """ file """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'out': 'file'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('file/', options['out_dir'])
        self.assertEqual('', options['out_file_name'])

    def test_process_default_compress_to_file_tar_gz(self):
        """ -c file.tar.gz """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True,
                               'out': 'file.tar.gz'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('./', options['out_dir'])
        self.assertEqual('file.tar.gz', options['out_file_name'])

    def test_process_default_compress_to_file_tar(self):
        """ -c file.tar """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True,
                               'out': 'file.tar'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('./', options['out_dir'])
        self.assertEqual('file.tar', options['out_file_name'])

    def test_process_default_compress_to_dir(self):
        """ -c dir/ """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True,
                               'out': 'dir/'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('dir/', options['out_dir'])
        self.assertEqual('baidu_irepo-test_bundle_1-0-0-1_bundle.tar.gz', options['out_file_name'])

    def test_process_default_compress_to_file(self):
        """ -c file """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True,
                               'out': 'file'})
        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('./', options['out_dir'])
        self.assertEqual('file.tar.gz', options['out_file_name'])

    def test_process_default_compress_to_multi_file(self):
        """ -c dir/file """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True,
                               'out': 'dir/file'})

        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('dir/', options['out_dir'])
        self.assertEqual('file.tar.gz', options['out_file_name'])

    def test_process_default_compress_to_multi_file_tar_gz(self):
        """ -c dir/file.tar.gz """
        options = KeySafeDict({'repo_type': 'bundle',
                               'name': 'baidu/irepo-test/bundle:1.0.0.1',
                               'compress': True,
                               'out': 'dir/file'})

        arg_processor = BundlePullArgsProcessor(options=options)
        arg_processor.process_rec()
        self.assertEqual('1.0.0.1', options['resource'])
        self.assertEqual('releases', options['resource_type'])
        self.assertEqual('dir/', options['out_dir'])
        self.assertEqual('file.tar.gz', options['out_file_name'])

