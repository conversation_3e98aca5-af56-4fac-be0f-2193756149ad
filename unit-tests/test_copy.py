""" test frame copy """
import unittest
import os
import shutil

from buildin.cli import local_tools


class TestCopyBase(unittest.TestCase):
    """
    src=file && dest=file :rename
    src=file && dest=dir  :move file to dest
    src=dir  && dest=dir  :move all in src to dest(not contains dir itself)
    src=dir  && dest=file :think dest file path as dir path
    """

    def setUp(self):
        self.src = None
        self.dest = None

    def _new_file(self, file):
        file = open(file, 'w')
        file.close()

    def _new_dir(self, dir):
        os.mkdir(dir)

    def test_copy(self):
        """ test """
        self._init_test_case()
        if self.src is None or self.dest is None:
            return
        try:
            local_tools.do_copy(self.src, self.dest)
            self._assert_result()
        finally:
            self._clean_workspace()

    def _init_test_case(self):
        pass

    def _assert_result(self):
        pass

    def _clean_workspace(self):
        self._clean(self.dest)
        self._clean(self.src)

    def _clean(self, path):
        if path is None:
            return
        if os.path.isdir(path):
            shutil.rmtree(path)
        elif os.path.isfile(path):
            os.remove(path)


class TestCopyRename(TestCopyBase):
    """ src=file && dest=file :rename """

    def _init_test_case(self):
        self.src = 'src-file'
        self.dest = 'dest-file'
        self._new_file(self.src)

    def _assert_result(self):
        self.assertTrue(os.path.isfile(self.dest))


class TestCopyFileToDir(TestCopyBase):
    """ src=file && dest=dir  :move file to dest """

    def _init_test_case(self):
        self.src = 'src-file'
        self.dest = 'dest-dir'
        self._new_file(self.src)
        self._new_dir(self.dest)

    def _assert_result(self):
        self.assertTrue(os.path.isfile(self.dest + '/' + self.src))


class TestCopyDirToExistsDir(TestCopyBase):
    """ src=dir && dest=dir  :move all in src to dest(not contains dir itself """

    def _init_test_case(self):
        self.src = 'src-dir'
        self.dest = 'dest-dir'
        self._new_dir(self.src)
        self._new_file(self.src + '/f1')
        self._new_file(self.src + '/f2')
        self._new_dir(self.dest)

    def _assert_result(self):
        self.assertTrue(os.path.isfile(self.dest + '/f1'))
        self.assertTrue(os.path.isfile(self.dest + '/f2'))
        self.assertFalse(os.path.isdir(self.dest + '/' + self.src))


class TestCopyDirToNotExistsDir(TestCopyBase):
    """ src=dir && dest=dir  :move all in src to dest(not contains dir itself """

    def _init_test_case(self):
        self.src = 'src-dir'
        self.dest = 'dest-dir'
        self._new_dir(self.src)
        self._new_dir(self.src + '/subdir')
        self._new_file(self.src + '/subdir/subf1')
        self._new_file(self.src + '/f1')
        self._new_file(self.src + '/f2')
        self._new_dir(self.dest)

    def _assert_result(self):
        self.assertTrue(os.path.isfile(self.dest + '/f1'))
        self.assertTrue(os.path.isfile(self.dest + '/f2'))
        self.assertTrue(os.path.isfile(self.dest + '/subdir/subf1'))
        self.assertFalse(os.path.isdir(self.dest + '/' + self.src))


class TestCopyDirToFileAsDirPath(TestCopyBase):
    """ src=dir  && dest=file :think dest file path as dir path """

    def _init_test_case(self):
        self.src = 'src-dir'
        self.dest = 'dest-dir'
        self._new_dir(self.src)
        self._new_file(self.src + '/f1')
        self._new_file(self.src + '/f2')

    def _assert_result(self):
        self.assertTrue(os.path.isfile(self.dest + '/f1'))
        self.assertTrue(os.path.isfile(self.dest + '/f2'))
        self.assertFalse(os.path.isdir(self.dest + '/' + self.src))
