# -* encoding:utf-8 -*-
""" 文件名测试 """

import importlib
from collections import namedtuple
from unittest import TestCase

from buildin.cli import request_tools


class TestGetRemoteFileNameByResponse(TestCase):
    """
    TestGetRemoteFileNameByResponse
    """

    def test_get_remote_file_name_by_response_normal(self):
        """
        test_get_remote_file_name_by_response_normal
        """
        Response = namedtuple('Response', ['headers'])
        res = Response(headers={
            'Content-Disposition': 'attachment; filename=feature_nir-CDNN-pt-wd.model.int32-1.0.1.109887.tar.gz'
        })
        self.assertEqual('feature_nir-CDNN-pt-wd.model.int32-1.0.1.109887.tar.gz',
                         request_tools.get_remote_file_name_by_response(res))

    def test_get_remote_file_name_by_response(self):
        """
        test_get_remote_file_name_by_response
        """
        import sys
        importlib.reload(sys)
        Response = namedtuple('Response', ['headers'])
        res = Response(headers={
            'Content-Disposition': 'attachment; filename=附件2 - 员工搬迁信息对应表(EE工程效能部)_baiwei.xlsx-5134d222c191'
        })
        self.assertEqual('附件2 - 员工搬迁信息对应表(EE工程效能部)_baiwei.xlsx-5134d222c191',
                         request_tools.get_remote_file_name_by_response(res))
