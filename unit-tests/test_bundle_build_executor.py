# -*- coding:utf-8 -*-
""" 测试 BundleBuildExecutor """
import json
import os
import pprint
from collections import namedtuple
from unittest import TestCase
from mock import patch

from buildin.cli.artifact import Artifact
from buildin.executors.bundle_build import BundleBuildExecutor
from buildin.executors.bundle_build import ArtifactOperation


class TestBundleBuildExecutor(TestCase):
    """ BundleBuildExecutor Test """

    @patch('buildin.cli.request_tools.check_response')
    @patch('buildin.cli.request_tools.do_get')
    def test__pretreat(self, do_get, check_response):
        """ 测试预处理 """
        Response = namedtuple('Response', ['code', 'content'])

        do_get.return_value = Response(code=200, content=json.dumps(
            {'revision': '6e297ab706d94dd8a795ba71cb6bf1aa',
             'roll_back': False}))
        check_response.return_value = None
        bundle = {
            'models': [
                {
                    'repotype': 'model',
                    'reponame': 'baidu/irepo-test/bundle',
                    'version': '0.0.1.145337@release',
                    'src': ['lib.a', 'lib.so'],
                    'dest': './'
                },
                {
                    'repotype': 'model',
                    'reponame': 'baidu/irepo-test/bundle',
                    'version': '0.0.1.145337@release',
                    'src': ['app.yml', 'conf.yml'],
                    'dest': './conf'
                },
                {
                    'repotype': 'model',
                    'reponame': 'baidu/irepo-test/bundle',
                    'version': '6e297ab706d94dd8a795ba71cb6bf1aa',
                    'src': ['app.yml', 'conf.yml'],
                    'dest': './conf'
                }
            ],
            'prods': [
                {
                    'repotype': 'prod',
                    'reponame': 'baidu/irepo-test/bundle',
                    'version': 'current_build@',
                    'src': ['./output/lib.a', './output/lib/'],
                    'dest': './conf/'
                },
                {
                    'repotype': 'prod',
                    'reponame': 'baidu/irepo-test/bundle',
                    'version': 'current_build@',
                    'src': ['./output/bin/'],
                    'dest': './conf/bin/'
                }
            ]
        }
        bundle_executor = BundleBuildExecutor()
        artifact_operations = bundle_executor._BundleBuildExecutor__pretreat(bundle)
        self.assertEqual(len(artifact_operations), 3)
        index = 0
        for op in artifact_operations:
            if index == 0:
                pprint.pprint(op)
                self.assertEqual(op.artifact.repo_type, 'model')
                self.assertEqual(op.artifact.repo_name, 'baidu/irepo-test/bundle')
                self.assertEqual(op.artifact.resource_type, 'releases')
                self.assertEqual(op.artifact.resource, '0.0.1.145337')
                self.assertEqual(len(op.operations), 4)
            if index == 1:
                pprint.pprint(op)
                self.assertEqual(op.artifact.repo_type, 'model')
                self.assertEqual(op.artifact.repo_name, 'baidu/irepo-test/bundle')
                self.assertEqual(op.artifact.resource_type, 'nodes')
                self.assertEqual(op.artifact.resource, '6e297ab706d94dd8a795ba71cb6bf1aa')
                self.assertEqual(len(op.operations), 2)
            if index == 2:
                pprint.pprint(op)
                self.assertEqual(op.artifact.repo_type, 'prod')
                self.assertEqual(op.artifact.repo_name, 'baidu/irepo-test/bundle')
                self.assertEqual(op.artifact.resource_type, 'current_build')
                self.assertEqual(op.artifact.resource, 'current_build')
                self.assertEqual(len(op.operations), 3)
            index += 1

    @patch('buildin.cli.request_tools.check_response')
    @patch('buildin.cli.request_tools.do_get')
    def test_merge_artifact_operations(self, do_get, check_response):
        """ 测试merge操作 """

        Response = namedtuple('Response', ['code', 'content'])
        do_get.return_value = Response(code=200, content=json.dumps(
            {'revision': '6e297ab706d94dd8a795ba71cb6bf1aa',
             'roll_back': False}))
        check_response.return_value = None
        # 总字典
        # 总字典中的制品
        model_artifact = Artifact.create_artifact(repo_type='model', repo_name='baidu/a/b',
                                                  version='uRoq3sBzPKwp6Fr9rGbFU3FTNvcb6tbw')
        model_artifact_op = ArtifactOperation(model_artifact)
        model_artifact_op.add_operation('./conf/', '.')

        artifact_operations = [model_artifact_op]

        # 子制品1
        child_model_artifact = Artifact.create_artifact(repo_type='model', repo_name='baidu/a/b',
                                                        version='uRoq3sBzPKwp6Fr9rGbFU3FTNvcb6tbw')
        child_model_artifact_op = ArtifactOperation(child_model_artifact)
        child_model_artifact_op.add_operation('./conf/a.conf', './conf/')

        # 子制品2
        child_model_artifact2 = Artifact.create_artifact(repo_type='model', repo_name='baidu/c/d',
                                                         version='1.0.1.1@release')
        child_model_artifact2_op = ArtifactOperation(child_model_artifact2)
        child_model_artifact2_op.add_operation(['a', 'b'], 'lib/')

        # bundle子字典
        child_bundle_artifact2_operations = [child_model_artifact_op, child_model_artifact2_op]
        bundle_out = 'bundle/'
        bundle_executor = BundleBuildExecutor()
        bundle_executor._BundleBuildExecutor__merge_artifact_operations(artifact_operations,
                                    child_bundle_artifact2_operations, bundle_out)
        self.assertEqual(len(artifact_operations), 2)
        index = 0
        for op in artifact_operations:
            if index == 0:
                pprint.pprint(op)
                self.assertEqual(op.artifact.repo_type, 'model')
                self.assertEqual(op.artifact.repo_name, 'baidu/a/b')
                self.assertEqual(op.artifact.resource_type, 'nodes')
                self.assertEqual(op.artifact.resource, 'uRoq3sBzPKwp6Fr9rGbFU3FTNvcb6tbw')
                self.assertEqual(len(op.operations), 2)
                self.assertEqual(op.operations[0]['src'], './conf/')
                self.assertEqual(op.operations[0]['dest'], '.')
                self.assertEqual(op.operations[1]['src'], './conf/a.conf')
                self.assertEqual(op.operations[1]['dest'], os.path.join(bundle_out, './conf/'))
            if index == 1:
                pprint.pprint(op)
                self.assertEqual(op.artifact.repo_type, 'model')
                self.assertEqual(op.artifact.repo_name, 'baidu/c/d')
                self.assertEqual(op.artifact.resource_type, 'releases')
                self.assertEqual(op.artifact.resource, '1.0.1.1')
                self.assertEqual(len(op.operations), 2)
                self.assertEqual(op.operations[0]['src'], 'a')
                self.assertEqual(op.operations[0]['dest'], os.path.join(bundle_out, 'lib/'))
                self.assertEqual(op.operations[1]['src'], 'b')
                self.assertEqual(op.operations[1]['dest'], os.path.join(bundle_out, 'lib/'))
            index += 1
