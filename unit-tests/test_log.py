# -* encoding:utf-8 -*-
""" Log test"""
from unittest import TestCase

from mock import patch

from buildin.cli.console import Log, LogLevel
from buildin.cli.options import GlobalOptions


class LogTest(TestCase):
    """ test log """

    @patch('builtins.print')
    def test_quit_info_debug(self, p):
        """ test_quit_info_debug """
        GlobalOptions.quiet = True
        Log.debug("debug")
        Log.debug("info")
        p.assert_not_called()

    @patch('buildin.cli.console.ConsoleTools.stderr_flush')
    def test_quit_error(self, sew):
        """ test_quit_error """
        GlobalOptions.quiet = True
        Log.error("error")
        sew.assert_called()

    @patch('builtins.print')
    def test_debug(self, p):
        """ test_debug """
        Log.set_level(LogLevel.INFO)
        Log.debug("debug")
        p.assert_not_called()

    @patch('buildin.cli.console.ConsoleTools.stderr_flush')
    @patch('builtins.print')
    def test_error(self, p, stw):
        """ test_error """
        Log.set_level(LogLevel.ERROR)
        Log.debug("debug")
        Log.info("info")
        p.assert_not_called()
        Log.error("error")
        stw.assert_called()
