""" LocalArtifact test """
from unittest import TestCase

from mock import mock

from buildin.cli.artifact import Artifact
from buildin.cli.exception import IRepoException


class TestLocalArtifact(TestCase):
    """ LocalArtifact Test """

    @mock.patch('os.path.isdir')
    def test_do_cache(self, path_id_dir):
        """ do_cache """
        path_id_dir.return_value = False
        current_arti = self.__create_arti()
        with self.assertRaises(IRepoException):
            current_arti.do_cache()

    def test_get_files_dir(self):
        """ get_files_dir """
        current_arti = self.__create_arti()
        self.assertEqual(current_arti.get_files_dir(), './')

    def __create_arti(self):
        """ create arti """
        repo_type = 'bundle'
        repo_name = 'baidu/agile/test'
        current_build = 'current_build@'
        return Artifact.create_artifact(repo_type, repo_name, current_build)
