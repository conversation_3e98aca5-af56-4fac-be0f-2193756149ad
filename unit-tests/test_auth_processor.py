""" argparse frame test"""
import unittest

from mock import patch

from buildin.executors.abstract import RootArgsProcessor


class TestRootArgProcessor(unittest.TestCase):
    """ RootArgProcessor Test """

    @patch('buildin.cli.giano.get_cred')
    def test_process_auth_with_user_token(self, get_cred):
        """ user token """
        options = {
            'user': 'xujinfeng',
            'token': 'token',
            'disable_giano': False
        }
        root_prc = RootArgsProcessor(options)
        root_prc._RootArgsProcessor__process_auth()
        get_cred.assert_not_called()

