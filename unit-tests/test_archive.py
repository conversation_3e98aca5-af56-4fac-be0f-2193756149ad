# -* encoding:utf-8 -*-
""" archive.py test """
import unittest

from mock import patch

from buildin.cli import archive
from buildin.cli.archive import TarArchive


class TestArchive(unittest.TestCase):
    """ archive Test """
    def test_get_archive_handler_is_none(self):
        """ test_get_archive_handler_is_none """
        archiveHandler = archive.get_archive_handler('file-name')
        self.assertIsNone(archiveHandler)

    def test_get_archive_handler_targz(self):
        """ test_get_archive_handler_targz """
        archiveHandler = archive.get_archive_handler('file-name.tar')
        self.assertEqual(archiveHandler.__name__, archive.TarArchive.__name__)

    @patch("buildin.cli.local_tools.create_path_dir")
    @patch("buildin.cli.local_tools.remove")
    @patch("buildin.cli.archive.TarArchive._do_create")
    def test_create(self, do_create, remove, create_path_dir):
        """ """
        src_dir = "./src"
        target_file_name = "file"
        target_path = "./dest"
        TarArchive.create(src_dir, target_file_name, target_path)
        create_path_dir.assert_called_with(target_path + '/')
        remove.assert_called_with(src_dir)
        do_create.assert_called_with(src_dir, target_file_name + '.tar', target_path + '/')