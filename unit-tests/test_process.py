""" process.py test """
import unittest

from buildin.cli.process_bar import SingleProcessBar


class TestProcess(unittest.TestCase):
    """ process bar test """

    def test_process(self):
        """ process """
        process = SingleProcessBar(max_steps=100, title='TEST', done_info='DONE')
        process.set_process(10)
        self.assertEqual(process.percentage, 10)
        process.set_process()
        self.assertEqual(process.percentage, 11)
        process.disappear()
        process.finish()

    def test_process_overflow(self):
        """ process overflow size """
        process = SingleProcessBar(max_steps=100, title='TEST', done_info='DONE')
        process.set_process(101)
        self.assertEqual(process.percentage, 100)
