# -* encoding:utf-8 -*-
""" KeySafeDict测试 """
from unittest import TestCase

from buildin.cli.options import KeySafeDict


class TestKeySafeDict(TestCase):
    """ KeySafeDict测试 """

    def test_normal(self):
        """ 正常字典行为测试 """
        src_dict = {'a': 1, 'b': 2}
        safe_dict = KeySafeDict(src_dict)
        self.assertEqual(safe_dict['a'], 1)
        self.assertTrue('a' in src_dict)
        self.assertFalse('c' in src_dict)
        self.assertTrue(safe_dict.__contains__('a'))
        self.assertFalse(safe_dict.__contains__('c'))
        self.assertEqual(safe_dict.get('c', 1), 1)
        self.assertEqual(safe_dict.get('b', 1), 2)

    def test_get_invalid_key(self):
        """ 无效key测试 """
        src_dict = {'a': 1, 'b': 2}
        safe_dict = KeySafeDict(src_dict)
        self.assertIsNone(safe_dict['c'])
