---
description: 
globs: 
alwaysApply: true
---

在修改 PLAN.md 期间不要写代码

# 模型仓库管理系统

一个强大的命令行工具，用于管理AI模型的完整生命周期，支持模型的上传、下载、版本控制和权限管理。

## 业务目标

- 提供命令行工具管理AI模型的生命周期
- 支持模型的上传、下载、版本控制和权限管理
- 实现模型仓库的分布式存储和访问

## 核心功能

### 1. 空间管理(Space)
- 创建空间
- 列出空间
- 空间成员管理

### 2. 仓库管理(Repo)
- 创建仓库
- 列出仓库
- 仓库权限管理

### 3. 模型管理(Model)
- 多协议上传：支持HTTP/FTP/P2P等多种上传方式
- 安全下载：支持SHA256校验
- 版本控制：支持三/四位版本号
- 属性管理：支持模型属性的添加和管理

### 4. 权限管理(Grant)
- 用户权限分配
- 成员角色管理

### 5. 发布管理(Release)
- 模型版本发布
- 版本状态管理

## 系统架构

```
CLI层(repocli.py)
├─ 业务逻辑层(client/*)
│  ├─ 空间管理(addspace.py)
│  ├─ 仓库管理(addrepo.py)  
│  └─ 模型管理(addmodel.py)
│
├─ 框架层(buildin/*)
│  ├─ 命令解析(irepocli.py)
│  ├─ 执行器(executors/*)
│  └─ 提供者(providers/*)
│
└─ 网络层(frame/net/http.py)
```

## 主要数据实体

1. **空间(Space)**
   - 作为模型仓库的顶级容器
   - 管理多个仓库的集合

2. **仓库(Repo)**
   - 模型的逻辑分组
   - 属于特定空间

3. **模型(Model)**
   - 具体的模型文件
   - 包含元数据和实际文件

4. **版本(Revision)**
   - 模型的特定版本
   - 支持版本号管理

5. **发布(Release)**
   - 模型的稳定版本
   - 管理发布状态

## API接口

### 认证相关API
- `POST /reposervice/oper/login` - 用户登录
- `GET /reposervice/oper/userinfo` - 获取用户信息
- `GET /rest/v1/sessions/CURRENT` - 检查登录状态

### 空间管理API
- `POST /rest/v1/spaces` - 创建空间
- `GET /rest/v1/spaces` - 列出所有空间
- `PUT /rest/v1/spaces/{space}/members/{user}?role={role}` - 空间权限管理
- `GET /rest/v1/spaces/{space}/members` - 列出空间成员

### 仓库管理API
- `POST /rest/v1/spaces/{space}/repos` - 创建仓库
- `GET /rest/v1/spaces/{space}/repos` - 列出空间下的仓库
- `PUT /rest/v1/spaces/{space}/repos/{repo}/members/{user}?role={role}` - 仓库权限管理
- `GET /rest/v1/spaces/{space}/repos/{repo}/members` - 列出仓库成员

### 模型管理API
- `POST /rest/model/v3/baidu/{space}/{repo}/nodes` - 上传模型
- `GET /rest/v1/spaces/{space}/repos/{repo}/nodes` - 列出仓库中的模型
- `GET /rest/v1/spaces/{space}/repos/{repo}/nodes/{node}` - 获取模型版本详情
- `GET /rest/v1/spaces/{space}/repos/{repo}/nodes/{node}/files` - 下载模型文件
- `PATCH /rest/v1/spaces/{space}/repos/{repo}/nodes/{node}/properties` - 添加模型属性
- `POST /rest/model/v3/baidu/{space}/filenameCheck` - 检查文件名合法性

### 版本发布API
- `POST /rest/v1/spaces/{space}/repos/{repo}/releases` - 发布模型版本
- `GET /rest/v1/spaces/{space}/repos/{repo}/nodes/{node}/files/sha256` - 获取模型SHA256校验值

### 下载相关API
- `GET /rest/v1/spaces/{space}/repos/{repo}/nodes/{node}/files` - HTTP下载
- `POST /rest/v1/spaces/{space}/repos/{repo}/nodes/{node}/seeds` - P2P下载种子生成
- `GET /rest/v2/client/blobs/LATEST?os={os}&arch={arch}` - 客户端更新

### 辅助功能API
- `GET /reposervice/oper/progress` - 获取操作进度
- `GET /rest/v1/client/version` - 客户端版本更新

## 代码结构

### 1. 空间管理 (client/addspace.py)
```python
class SpaceServer:
    # 创建空间
    def add_space(space_name, save, comment)
    # 列出空间
    def list_space()
```

### 2. 仓库管理 (client/addrepo.py)
```python
class RepoServer:
    # 创建仓库
    def add_repo(space_name, repo_name, save, comment)
    # 列出仓库
    def list_repo(space_name)
```

### 3. 模型管理 (client/addmodel.py)
```python
class ModelServer:
    # 模型上传
    def add_local_model_pasv()  # 被动模式
    def add_local_model_port()  # 主动模式
    
    # 模型下载
    def fetch_revision()      # HTTP下载
    def fetch_revision_p2p()  # P2P下载
    def check_sha256()       # SHA256校验
    
    # 模型管理
    def list_model()
    def get_revision_detail()
    def add_props()
    def release()
```

### 4. 权限管理 (client/grant.py)
```python
class GrantServer:
    # 授权操作
    def add_grant(uname, level, spacename, reponame)
    def list_member(spacename, reponame)
```

## 权限级别

- MANAGER：管理员权限
- WRITER：写入权限
- READER：读取权限
- NONE：无权限

## 辅助功能

### 版本管理 (common/version_utils.py)
- 支持三位版本号(1.0.0)
- 支持四位版本号(1.0.0.1)
- 版本号比较和验证

### 配置管理 (common/conf.py)
- 保存空间和仓库配置
- 管理用户偏好设置

### 常量定义 (common/constant.py)
- API端点定义
- 系统常量配置

## 主要功能关系

1. 空间(Space)是最顶层的组织单位
2. 仓库(Repo)属于特定空间，用于组织模型
3. 模型(Model)存储在特定仓库中，支持版本控制
4. 权限管理贯穿整个系统，可以在空间和仓库级别进行控制