# encoding: utf-8
"""
constant value
"""

ENV = 'online'
TIMEOUT = 3600

LOGIN_URL = '/reposervice/oper/login'
USER_INFO_URL = '/reposervice/oper/userinfo'
ADD_SPACE_URL = '/rest/v1/spaces'
ADD_REPO_URL = '/rest/v1/spaces/%s/repos'
ADD_MODEL_URL = '/rest/model/v3/baidu/%s/%s/nodes'
ADD_PROPS_URL = '/rest/v1/spaces/%s/repos/%s/nodes/%s/properties'
ADD_RELEASE_URL = '/rest/v1/spaces/%s/repos/%s/releases'
GET_PROGRESS_URL = '/reposervice/oper/progress'
GET_REVISION_DETAIL_URL = '/rest/v1/spaces/%s/repos/%s/nodes/%s'
GET_REVISION_FILE_URL = '/rest/v1/spaces/%s/repos/%s/nodes/%s/files'
GET_SHA256_URL = '/rest/v1/spaces/%s/repos/%s/nodes/%s/files/sha256'
MK_GKO3_SEED_URL = '/rest/v1/spaces/%s/repos/%s/nodes/%s/seeds'
LIST_SPACE_URL = '/rest/v1/spaces'
LIST_REPO_URL = '/rest/v1/spaces/%s/repos'
LIST_MODEL_URL = '/rest/v1/spaces/%s/repos/%s/nodes'
UPDATE_URL = '/rest/v1/client/version'
CHECK_LOGIN_URL = '/rest/v1/sessions/CURRENT'
GRANT_SPACE_URL = '/rest/v1/spaces/%s/members/%s?role=%s'
GRANT_REPO_URL = '/rest/v1/spaces/%s/repos/%s/members/%s?role=%s'
LIST_SPACE_MEMBER_URL = '/rest/v1/spaces/%s/members'
LIST_REPO_MEMBER_URL = '/rest/v1/spaces/%s/repos/%s/members'
CHECK_FILENAME_URL = '/rest/model/v3/baidu/%s/filenameCheck'
REPOCLI_GOLANG_DOWN_URL = '/rest/v2/client/blobs/LATEST?os=%s&arch=%s'
SERVER_PORT = '8301'
ADD_MODEL_PASV = True
DOWNLOAD_P2P = False

if ENV == 'online':
    HOST = 'irepo.baidu-int.com'
    DEBUG = False
else:
    # HOST = 'localhost:8301'
    HOST = 'irepotest.baidu-int.com'
    DEBUG = True

file_path = ''
is_terminated = False
gko3_seed = 'NONE'
uname = ''
token = ''
real_server = ''
use_giano = True
giano_credential = ''
