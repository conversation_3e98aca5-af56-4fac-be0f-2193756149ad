# -* encoding:utf-8 -*-
"""
版本号 工具类
"""


def compare(v1, v2):
    """
    v1 > v2 : return 1
    v1 = v2 : return 0
    v1 < v2 : return -1
    """
    version1_arr = v1.split('.')
    version2_arr = v2.split('.')
    index = 0
    while True:
        if index == len(version1_arr) and index == len(version2_arr):
            return 0
        if len(version1_arr) == index:
            version1_arr.append(0)
        if len(version2_arr) == index:
            version2_arr.append(0)
        if int(version1_arr[index]) > int(version2_arr[index]):
            return 1
        elif int(version1_arr[index]) < int(version2_arr[index]):
            return -1
        index += 1


def is_three_bit_version(version):
    """
    1.0.1 : return True
    1.0 : return False
    """
    if not version:
        return False

    version_elements = version.split('.')
    if len(version_elements) != 3:
        return False
    return __is_all_digit_elements(version_elements)


def is_four_bit_version(version):
    """
    1.0.1.1 : return True
    1.0 : return False
    """
    if not version:
        return False

    version_elements = version.split('.')
    if len(version_elements) != 4:
        return False

    return __is_all_digit_elements(version_elements)


def __is_all_digit_elements(version_elements):
    for version in version_elements:
        if not version.isdigit():
            return False
    return True
