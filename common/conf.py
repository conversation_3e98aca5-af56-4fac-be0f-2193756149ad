# -* encoding:utf-8 -*-
"""
conf class
"""

import configparser
import os
import sys

from common import constant


class Config(object):
    USER_DATA_CONF_NAME = "userdata.conf"
    """
    process conf
    """

    def __init__(self):
        self.conf_path = os.path.join(sys.path[0], Config.USER_DATA_CONF_NAME)
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        sections = conf.sections()

        changed = False
        sec_space_name = "space_info"
        if sec_space_name not in sections:
            conf.add_section(sec_space_name)
            changed = True
        opt = conf.options(sec_space_name)
        if "name" not in opt:
            conf.set(sec_space_name, "name", "")
            changed = True

        sec_repo_name = "repo_info"
        if sec_repo_name not in sections:
            conf.add_section(sec_repo_name)
            changed = True
        opt = conf.options(sec_repo_name)
        if "name" not in opt:
            conf.set(sec_repo_name, "name", "")
            changed = True

        sec_uinfo_name = "user_info"
        if sec_uinfo_name not in sections:
            conf.add_section(sec_uinfo_name)
            changed = True
        opt = conf.options(sec_uinfo_name)
        if "uname" not in opt:
            conf.set(sec_uinfo_name, "uname", "")
            changed = True
        if "token" not in opt:
            conf.set(sec_uinfo_name, "token", "")
            changed = True

        sec_cli_info_name = "cli_info"
        if sec_cli_info_name not in sections:
            conf.add_section(sec_cli_info_name)
            changed = True
        opt = conf.options(sec_cli_info_name)
        if "auto_update" not in opt:
            conf.set(sec_cli_info_name, "auto_update", str(True))
            changed = True

        if changed:
            self.__save_conf(conf)
            os.chmod(self.conf_path, 0o600)
        self.sec_space_name = sec_space_name
        self.sec_repo_name = sec_repo_name
        self.sec_uinfo_name = sec_uinfo_name
        self.sec_cli_info_name = sec_cli_info_name

    def get_token(self):
        """
        get user token
        首先查找使用--token参数指定的token，如果没有则查找持久化到配置中的token
        """
        if constant.token:
            return constant.token
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        return conf.get(self.sec_uinfo_name, "token")

    def get_uname(self):
        """
        get user name
        首先查找使用--user参数指定的user，如果没有则查找持久化到配置中的user
        """
        if constant.uname:
            return constant.uname
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        return conf.get(self.sec_uinfo_name, "uname")

    def get_space(self):
        """
        get default space
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        return conf.get(self.sec_space_name, "name")

    def get_repo(self):
        """
        get default repo
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        return conf.get(self.sec_repo_name, "name")

    def get_is_auto_update(self):
        """
        is auto update
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        return conf.getboolean(self.sec_cli_info_name, "auto_update")

    def set_space(self, space_name):
        """
        set default space
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        conf.set(self.sec_space_name, "name", space_name)
        self.__save_conf(conf)

    def set_repo(self, repo_name):
        """
        set default repo
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        conf.set(self.sec_repo_name, "name", repo_name)
        self.__save_conf(conf)

    def set_uname(self, uname):
        """
        set user name
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        conf.set(self.sec_uinfo_name, "uname", uname)
        self.__save_conf(conf)

    def set_token(self, token):
        """
        set user token
        """
        conf = configparser.ConfigParser()
        conf.read(self.conf_path)
        conf.set(self.sec_uinfo_name, "token", token)
        self.__save_conf(conf)

    def __save_conf(self, conf):
        with open(self.conf_path, "w") as f:
            conf.write(f)
