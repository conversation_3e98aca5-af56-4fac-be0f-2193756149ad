# -* encoding:utf-8 -*-

import argparse


class ArgvPaser(object):
    def __init__(self):
        self.prog = "repocli"
        epilog = "need help? hi langshiquan"
        usage = "\n" \
                "[ repocli -h ]             For common usage.\n" \
                "[ repocli bundle -h ]         For more operation usage.\n"
        self.main_parser = argparse.ArgumentParser(
            prog=self.prog,
            formatter_class=argparse.RawTextHelpFormatter,
            epilog=epilog,
            usage=usage
        )

        self.main_parser.add_argument(
            "--grant",
            nargs=2,
            action="store",
            dest="grant",
            metavar=("USER", "ROLE"),
            default=None,
            help="grand user specify auth, MANAGER/WRITER/READER/NONE"
        )

        self.main_parser.add_argument(
            "--listmembers",
            action="store_true",
            dest="listmember",
            help="list space or repo members and permission"
        )

        self.main_parser.add_argument(
            "--addspace",
            action="store",
            dest="addspace",
            metavar="SPACE_NAME",
            default=None,
            help="add space, you will be the space's manager"
        )

        self.main_parser.add_argument(
            "--addrepo",
            action="store",
            dest="addrepo",
            metavar="REPO_NAME",
            default=None,
            help="add repo, you will be the repo's manager"
        )

        self.main_parser.add_argument(
            "--addmodel",
            action="store",
            dest="addmodel",
            metavar="MODEL_ADDR",
            default=None,
            help="upload model files, with --uploadmode to specify upload mode, default PASV"
        )
        self.main_parser.add_argument(
            "--uploadmode",
            action="store",
            dest="uploadmode",
            metavar='UPLOAD_MODE',
            help="specify upload mode: PASV (default) or PORT mode"
        )

        self.main_parser.add_argument(
            "--addprops",
            action="store",
            dest="addprops",
            metavar="REVISION",
            default=None,
            help="add properties on specified revision, must be used with --prop"
        )

        self.main_parser.add_argument(
            "--prop",
            nargs=2,
            action="append",
            dest="props",
            metavar=("KEY", "VALUE"),
            default=None,
            help="specify property key and value , can be specified multiple times"
        )

        self.main_parser.add_argument(
            "--release",
            action="store",
            dest="release",
            metavar="REVISION",
            default=None,
            help="release model revision, must use --version to specify three/four bit version"
        )

        self.main_parser.add_argument(
            "--version",
            action="store",
            dest="version",
            metavar="VERSION",
            default=None,
            help="three/four bit version like 1.0.0/*******"
        )

        self.main_parser.add_argument(
            "--disable-giano",
            action="store_true",
            dest="disable_giano",
            help="disable giano and use user & token. "
                 "otherwise repocli use giano to authenticate with relay-login user default"
        )

        self.main_parser.add_argument(
            "--specifyuser",
            action="store",
            dest="specifyuser",
            metavar="SPECIFY_USER",
            default=None,
            help="specify user name"
        )
        self.main_parser.add_argument(
            "--specifytoken",
            action="store",
            dest="specifytoken",
            metavar="SPECIFY_TOKEN",
            default=None,
            help="specify user token"
        )

        self.main_parser.add_argument(
            "--user",
            action="store",
            dest="user",
            metavar="USER",
            default=None,
            help="specify user name on param , not save"
        )
        self.main_parser.add_argument(
            "--token",
            action="store",
            dest="token",
            metavar="TOKEN",
            default=None,
            help="specify user token on param , not save. "
                 "this param will disable giano"
        )

        self.main_parser.add_argument(
            "--spacename",
            action="store",
            dest="spacename",
            metavar="SPACE_NAME",
            default=None,
            help="specify space name"
        )
        self.main_parser.add_argument(
            "--reponame",
            action="store",
            dest="reponame",
            metavar="REPO_NAME",
            default=None,
            help="specify repo name"
        )
        self.main_parser.add_argument(
            "-c",
            "--comment",
            action="store",
            dest="comment",
            metavar="COMMENT",
            default=None,
            help="comment input"
        )
        self.main_parser.add_argument(
            "--listspace",
            action="store_true",
            dest="listspace",
            help="list user's space"
        )
        self.main_parser.add_argument(
            "--listemptyspace",
            action="store_true",
            dest="listemptyspace",
            help="list user's empty spaces (spaces with no repositories)"
        )
        self.main_parser.add_argument(
            "--listrepo",
            action="store_true",
            dest="listrepo",
            help="list a space's repo"
        )
        self.main_parser.add_argument(
            "--listrev",
            action="store_true",
            dest="listrev",
            help="list a space-repo's revision"
        )
        self.main_parser.add_argument(
            "--revdetail",
            action="store",
            dest="revdetail",
            metavar='REVISION',
            help="show detail of specified revision"
        )
        self.main_parser.add_argument(
            "--fetchrev",
            action="store",
            dest="fetchrev",
            metavar='REVISION',
            help="fetch model, with --downloadmode to specify download mode, default HTTP"
        )
        self.main_parser.add_argument(
            "--downloadmode",
            action="store",
            dest="downloadmode",
            metavar='DOWNLOAD_MODE',
            help="specify download mode: HTTP (default) or P2P mode"
        )
        self.main_parser.add_argument(
            "--out",
            action="store",
            dest="out",
            metavar='FILENAME',
            help="output path of fetched revision"
        )
        self.main_parser.add_argument(
            "-s",
            "--save",
            action="store_true",
            dest="save",
            help="save to default choice when add space or repo"
        )
        self.main_parser.add_argument(
            "--specifyspace",
            action="store",
            dest="specifyspace",
            metavar="SPACE_NAME",
            default=None,
            help="specify default space name"
        )
        self.main_parser.add_argument(
            "--specifyrepo",
            action="store",
            dest="specifyrepo",
            metavar="REPO_NAME",
            default=None,
            help="specify default repo name"
        )
        self.main_parser.add_argument(
            "--default",
            action="store_true",
            dest="default",
            help="show default space name and repo name"
        )
        self.main_parser.add_argument(
            "-v",
            action="store_true",
            dest="v",
            default=False,
            help="print client version and exit"
        )

    def get_main_parser(self):
        return self.main_parser
