# -* encoding:utf-8 -*-
import socket
import subprocess
from common import constant
import random

ROUTER_BNS_LIST = {"north": "group.irepo-server-route.agile.all",
                   "south": "group.irepo-server-route.agile.all"}
SOUTH_LIST = ["nj", "hz", "nj02", "hz01"]


class BnsParser(object):
    """
    bns parser
    """

    def __init__(self):
        pass

    def get_url(self, uri):
        """
        get url from bns and uri
        """
        if constant.real_server != '':
            return constant.real_server + uri
        if constant.ENV == 'online':
            hostname = socket.gethostname()
            idc = hostname.split('-', 1)[0]
            if idc in SOUTH_LIST:
                bns = ROUTER_BNS_LIST['south']
            else:
                bns = ROUTER_BNS_LIST['north']
            try:
                ret = subprocess.check_output("get_instance_by_service -p " + bns, shell=True)
                if ret[-1] == '\n':
                    ret = ret[:-1]
                machines = ret.split('\n')
                machine = random.choice(machines)
                tmp = machine.split(' ')
                constant.real_server = "http://" + tmp[0] + ":" + constant.SERVER_PORT
            except Exception as e:
                # print error message of exception
                print(str(e))
                constant.real_server = "https://" + constant.HOST
        else:
            constant.real_server = "http://" + constant.HOST
        return constant.real_server + uri
