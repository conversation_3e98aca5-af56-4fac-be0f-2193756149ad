# -*- coding:utf-8 -*-
from os import path
from http.server import BaseHTTPRequestHandler, HTTPServer
import common.constant as constant


class MyHttpHandler(BaseHTTPRequestHandler):
    notify_condition = None
    print_helper = None

    def do_GET(self):
        try:
            MyHttpHandler.notify_condition.acquire()
            if self.path == "/upload/%s" % path.basename(constant.file_path):
                with open(constant.file_path, "rb") as f:
                    filesize = path.getsize(constant.file_path)
                    filename = path.basename(constant.file_path)
                    self.send_response(200)
                    self.send_header('Content-type', 'Content-Type: application/octet-stream')
                    self.send_header('Content-Disposition', 'attachment; filename="%s"' % filename)
                    self.send_header('Content-Length', filesize)
                    self.end_headers()

                    percentage = 0
                    mb_read = 0
                    mb_file = filesize / (1024.0 * 1024.0)
                    while True:
                        MyHttpHandler.print_helper.info(
                                "uploading model ... %d%% %.2fMB/%.2fMB\r" % (
                                    percentage, mb_read, mb_file),
                                percentage >= 100)
                        if constant.is_terminated:
                            return
                        data = f.read(1024 * 1024 * 8)  # read 8MB each time
                        if not data:
                            break
                        self.wfile.write(data)
                        mb_read += 8
                        if mb_read > mb_file:
                            mb_read = mb_file
                        percentage = mb_read * 100.0 / mb_file
                    f.close()
            elif self.path.startswith("/gko3/start/"):
                # notify request from repo server with gko3 seed info hash
                # /gko3/start/bf8b8ccd8064d19e16f4ecea6c305aceee577aac
                constant.gko3_seed = self.path.replace("/gko3/start/", "")
                self.send_response(200, "OK")
        except IOError as e:
            self.send_error(404, 'File Not Found: %s' % self.path)
        finally:
            MyHttpHandler.notify_condition.notify()
            MyHttpHandler.notify_condition.release()


class Server:
    def __init__(self, print_helper, notify_condition):
        self.print_helper = print_helper
        MyHttpHandler.notify_condition = notify_condition
        MyHttpHandler.print_helper = print_helper
        self.server = HTTPServer(('', 0), MyHttpHandler)
        self.server_name = self.server.server_name
        self.server_port = self.server.server_port

    def start_server(self):
        """
        start file upload server
        """
        self.server.serve_forever()

    def stop_server(self):
        """
        stop file upload server
        """
        self.server.shutdown()
        self.server.socket.close()
