pkgname=repocli
pkgver=1.0
pkgrel=1
pkgdesc="client for artifact"
depends=('python-pip' 'curl')
sources=()
md5sums=()

PROTOCOL="https"
IREPO_HOST="irepo.baidu-int.com"
URL="${PROTOCOL}://"$IREPO_HOST"/rest/v1/client/version"

jumbo_install() {
	install_root="${pkgdir}/${JUMBO_ROOT}/opt/${pkgname}"
	mkdir -p "${pkgdir}/${JUMBO_ROOT}/bin"
	mkdir -p ${install_root}
	mkdir -p "${srcdir}/${pkgname}-${pkgver}"
	cd "${srcdir}/${pkgname}-${pkgver}"
	VERSION_INFO=$(curl -k "$URL" -H "Host:$IREPO_HOST" 2>/dev/null)
	PRODUCT_PATH=$(echo ${VERSION_INFO} | egrep -o "\"productHttpPath\":\".*?\"" | awk -F '\"' '{print $4}')
	VERSION=$(echo ${VERSION_INFO} | egrep -o "\"version\":\".*?\"" | awk -F '\"' '{print $4}')
	MD5_EXPECTED=$(echo ${VERSION_INFO} | egrep -o "\"md5\":\".*?\"" | awk -F '\"' '{print $4}')
	curl -o output.tar.gz ${PRODUCT_PATH}
	tar -zxf output.tar.gz
	rm output.tar.gz
	MD5_CURRENT=$(md5sum output/repoclient.tar.gz | awk '{print $1}')
	if [[ ${MD5_EXPECTED} != ${MD5_CURRENT} ]]; then
		echo 'Upload file md5sum check failed !'
		exit -1
	else
		echo 'Check md5sum done'
	fi
	tar xzf output/repoclient.tar.gz -C ${install_root}
	rm -rf output
	mkdir -p ~/.pip

cat <<END > ~/.pip/pip.conf
[global]
timeout = 60
index = http://pip.baidu.com/root/baidu/
index-url = http://pip.baidu.com/root/baidu/+simple/
trusted-host = pip.baidu.com
[list]
format = columns
END

	pip install "requests_toolbelt>=0.8.0"
	pip install "requests>=2.18.4"
	echo ${VERSION} > ${install_root}/version
	cd "${pkgdir}/${JUMBO_ROOT}/bin"
	rm -rf repocli
	ln -s "../opt/${pkgname}/repocli" "repocli"
}