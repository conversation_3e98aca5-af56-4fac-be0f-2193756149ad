#!/bin/bash

set -e

trap 'echo Error on line $BASH_SOURCE:$LINENO' ERR
DEFAULT_REPO_CLIENT_ROOT="$HOME/.irepo"
REPO_CLIENT_ROOT="$DEFAULT_REPO_CLIENT_ROOT"
FORCE=false
BETA=false

while getopts fbt: opt
do
    case ${opt} in
        b) BETA=true ;;
        f) FORCE=true ;;
        t) REPO_CLIENT_ROOT=${OPTARG} ;;
    esac
done

OS=`uname -s`
if [ ${OS} != "Darwin" -a ${OS} != "Linux" ]; then
    echo "Current OS ${OS} is not supported"
    exit 1
fi

if [[ "$FORCE" == false && -n "$REPO_CLIENT_ROOT" && -d "$REPO_CLIENT_ROOT" && -f "$REPO_CLIENT_ROOT/repocli" ]]
then
    echo "iRepo Client is already installed in your system"
    exit 1
fi

# 生产环境走https
PROTOCOL="http"
IREPO_HOST="irepotest.baidu-int.com"
URL="${PROTOCOL}://"$IREPO_HOST"/rest/v1/client/version"
CURL_ERROR_MESSAGE="======================================================================================\n"
CURL_ERROR_MESSAGE="${CURL_ERROR_MESSAGE}curl failed, maybe current curl version not supported https, should update curl: \n"
CURL_ERROR_MESSAGE="${CURL_ERROR_MESSAGE}for linux in IDC: jumbo install curl\n"
CURL_ERROR_MESSAGE="${CURL_ERROR_MESSAGE}======================================================================================\n"

# 校验curl是否支持https
curl -V | grep https > /dev/null 2>&1 || (echo -e $CURL_ERROR_MESSAGE && exit 1)

VERSION_INFO=$(curl -k "$URL" -H "Host:$IREPO_HOST" 2>/dev/null)

PRODUCT_PATH_KEY=productPath
MD5_KEY=md5
VERSION_KEY=version
if [ ${BETA} ];then
    PRODUCT_PATH_KEY=betaProductPath
    MD5_KEY=betaMd5
fi
PRODUCT_PATH=$(echo ${VERSION_INFO} | egrep -o "\"${PRODUCT_PATH_KEY}\":\".*?\"" | awk -F '\"' '{print $4}')
# 去掉最后的/
PRODUCT_PATH=${PRODUCT_PATH%/}
VERSION=$(echo ${VERSION_INFO} | egrep -o "\"${VERSION_KEY}\":\".*?\"" | awk -F '\"' '{print $4}')
MD5_EXPECTED=$(echo ${VERSION_INFO} | egrep -o "\"${MD5_KEY}\":\".*?\"" | awk -F '\"' '{print $4}')

echo "Latest version ${VERSION}"
mkdir -p "$REPO_CLIENT_ROOT"
REPO_CLIENT_ROOT=$(cd ${REPO_CLIENT_ROOT}; pwd)
echo "Repocli root: ${REPO_CLIENT_ROOT}"

cd ${REPO_CLIENT_ROOT}
curl -O ftp://${PRODUCT_PATH}/output/repoclient.tar.gz -u getprod:getprod 2>/dev/null

echo "Download package done"

# check md5
MD5_PATH=${PRODUCT_PATH}/${PRODUCT_PATH##*/}.md5
if [ ${OS} == "Darwin" ]; then
    MD5_CURRENT=$(md5 repoclient.tar.gz | awk '{print $4}')
else
    MD5_CURRENT=$(md5sum repoclient.tar.gz | awk '{print $1}')
fi

if [ ${MD5_EXPECTED} != ${MD5_CURRENT} ]; then
    echo 'Upload file md5sum check failed !'
    exit 1
else
    echo 'Check md5sum done'
fi

tar -zxf repoclient.tar.gz
rm repoclient.tar.gz

if [[ -n `python -c "import repocli"` ]]
then
    echo 'Need install dependencies...'
    LIB_INSTALL_SORT="setuptools-39.0.1 certifi-2018.1.18 chardet-3.0.4 idna-2.6 urllib3-1.22 requests-2.18.4 requests-toolbelt-0.8.0"

    mkdir -p ${REPO_CLIENT_ROOT}/lib/extract && cd ${REPO_CLIENT_ROOT}/lib/extract
    for LIB in ${REPO_CLIENT_ROOT}/lib/*.tar.gz; do tar -zxvf ${LIB}; done
    for LIB_DIR in ${LIB_INSTALL_SORT}; do cd ${LIB_DIR} && python setup.py build && python setup.py install ; cd .. ; done

    echo "Install repocli dependencies done"
fi

echo ${VERSION} > ${REPO_CLIENT_ROOT}/version

if ! grep "PATH=$REPO_CLIENT_ROOT:\$PATH" "$HOME/.bashrc" 1>/dev/null
then
    echo "export PATH=$REPO_CLIENT_ROOT:\$PATH" >> $HOME/.bashrc
    source $HOME/.bashrc
fi

echo "Install repocli done"
exit 0
