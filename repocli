#!/usr/bin/env python
# coding=utf-8

"""
Stub for launching repocli

"""
import sys
import subprocess
import platform  # Added import for platform module

ver = platform.python_version_tuple()  # Changed to use platform module
if ver[0] != '3':  # Changed to check for Python 3.x
    ver_s = ".".join(ver)  # Changed to join the version tuple
    print("('Python', '{}', 'is unsupported, please upgrade your python to 3.7.x')".format(ver_s))  # Changed to use format method

# 检查requests版本
try:
    import requests
    import requests_toolbelt
    from common import version_utils

    if version_utils.compare(requests.__version__, "2.18.4") < 0:
        raise ImportError("requests version must be >= 2.18.4")
except ImportError as e:
    print("requests not install, "
          "please visit 'https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/zANIpQEEV5/RZUAEyaK3yPQQJ' fix")
    print("import error: %s" % e)
    sys.exit(-1)

# 不显示ssl警告与安全请求警告
try:
    import urllib3
    from requests.packages.urllib3.exceptions import InsecureRequestWarning

    urllib3.disable_warnings()
    urllib3.disable_warnings(InsecureRequestWarning)
except ImportError as e:
    print("urllib3 not install, "
          "please visit 'https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/zANIpQEEV5/RZUAEyaK3yPQQJ' fix")
    sys.exit(-1)

# 检查更新
from client import version_update

if version_update.check_for_update():
    version_update.update()

from adapter import repocli_adapter
from adapter import repocli_golang_download
from buildin.cli.console import Log

adapted_args = repocli_adapter.get_adapted_args()
if adapted_args:
    repocli_golang_location = repocli_golang_download.download_if_necessary()
    if repocli_golang_location:
        full_args = []
        full_args.extend([repocli_golang_location])
        full_args.extend(adapted_args)
        Log.info('rerun args: %s' % full_args)
        status = subprocess.call(full_args)
        exit(status)
    else:
        Log.warn('download repocli-golang failed, please hi langshiquan')
        exit(-1)

# 执行命令
argv = sys.argv
if len(argv) > 1 and not str(argv[1]).startswith('-'):
    repocli = __import__('buildin.irepocli', fromlist=('irepocli',))
else:
    repocli = __import__('repocli')
repocli.main()
