#!/usr/bin/env bash
# $1: 环境
# e.g.: test、online、local
# $2: REPO_CLI
# e.g.: output/repocli
set -e
PROFILE=$1
REPO_CLI=$2

if [[ ${PROFILE} == 'local' ]]
then
	PROFILE='test'
	REPO_CLI=../repocli
fi

export BUNDLE_OUT=./bundle/
export FAILED_ASSERT=bundle_result.temp
TEST_MODULE=baidu/irepo-test/bundle
BUNDLE_FILE=${PROFILE}/bundle.yml
rm -rf ${BUNDLE_OUT}

${REPO_CLI} bundle build -f ${BUNDLE_FILE} --name ${TEST_MODULE} --out ${BUNDLE_OUT} --disable-giano --debug
:> ${FAILED_ASSERT}

# 判断文件或者目录是否存在，过滤掉注释
cat $PROFILE/bundle_build_assert.txt|grep -v "#"|awk '{
		bundle_out=ENVIRON["BUNDLE_OUT"];
		failed_assert=ENVIRON["FAILED_ASSERT"];
        cmd="if [ ! -"$1" "bundle_out$2" ];then echo "$1":"$2" >> "failed_assert";fi";
        system(cmd);
}'
# 检查bundle build构建结果
if [[ -s ${FAILED_ASSERT} ]] ;then
	cat ${FAILED_ASSERT}
	cat ${FAILED_ASSERT} | mail -s "[$PROFILE]client bundle test result" ${AGILE_TRIGGER_USER}@baidu.com
	exit -1
fi