#!/usr/bin/env bash
# $1: 环境
# e.g.: test、online、local
# $2: REPO_CLI
# e.g.: output/repocli


set -e
PROFILE=$1
REPO_CLI=$2

if [[ ${PROFILE} == 'local' ]]
then
	PROFILE='test'
	REPO_CLI=`pwd`/../repocli
fi

TEST_MODULE=baidu/irepo-test/bundle
BUNDLE_PULL_FILE=${PROFILE}/bundle_pull.yml
export FAILED_ASSERT=bundle_pull_result.temp
:> ${FAILED_ASSERT}

# CASE1:bundle push
revision=`${REPO_CLI} bundle push -f ${BUNDLE_PULL_FILE} --name ${TEST_MODULE} --debug --disable-giano --comment 'bundle script auto test'|grep revision |awk '{print $NF}'`
echo "revision:${revision}"
if [[ ! -n "${revision}" ]];then
	echo "push failed" >> ${FAILED_ASSERT}
	exit -1
fi

# 执行bundle pull
pull(){
	out=$1
	if [[ "${out}" != "./" ]]; then
		rm -rf ${out}
	fi
	compress=$2
	COMMAND="${REPO_CLI} bundle pull --name ${TEST_MODULE}@${revision} --mode HTTPS --disable-giano --debug --with-meta"
	if [[ "true" == "${compress}" ]]; then
		COMMAND+=" -c"
	fi
	if [[ "" != "${out}" ]]; then
		COMMAND+=" --out "
		COMMAND+=${out}
	fi
	echo ${COMMAND}
	${COMMAND}
}

# 判断文件或者目录是否存在，过滤掉注释
assert(){
	export baseOut=$1
	cat ${PROFILE}/bundle_pull_assert.txt|grep -v "#"|awk '{
		bundle_out=ENVIRON["PULL_EXTRACT_OUT"];
		failed_assert=ENVIRON["FAILED_ASSERT"];
		dir_prefix=ENVIRON["baseOut"];
        cmd="if [ ! -"$1" "dir_prefix"/"bundle_out$2" ];then echo "$1":"$2" >> "failed_assert";fi";
        system(cmd);
	}'
	check_failed_assert ${baseOut}
}
# 判断文件是否存在
assert_file_exists(){
	if [[ ! -f $1 ]];then
		echo "bundle pull $1 assert failed" > ${FAILED_ASSERT}
	fi
	check_failed_assert
}

# 检查断言结果
check_failed_assert(){
	if [[ -s ${FAILED_ASSERT} ]] ;then
		cat ${FAILED_ASSERT}
		(cat ${FAILED_ASSERT} && echo $1) | mail -s "[$PROFILE]client bundle test result" ${AGILE_TRIGGER_USER}@baidu.com
		exit -1
	fi
	:> ${FAILED_ASSERT}
}

DEFAULT_BUNDLE_NAME="baidu_irepo-test_bundle_"${revision:0:7}
META_FILENAME=${DEFAULT_BUNDLE_NAME}".bundle-debug.yml"

echo "CASE2(1):bundle pull --out single_dir/ -c"
PULL_SINGLE_OUT='temp/'

pull ${PULL_SINGLE_OUT} true
assert_file_exists ${PULL_SINGLE_OUT}${META_FILENAME}
cd ${PULL_SINGLE_OUT} && tar -xzf *.tar.gz && rm *.tar.gz
cd -
assert ${PULL_SINGLE_OUT}
rm -rf ${PULL_SINGLE_OUT}/*

echo "CASE2(2):bundle pull --out too/many/dir/"
PULL_EXTRACT_MULTI_OUT="backup/irepo-test/bundle/CIBUILD-bundle/r234567/"
pull ${PULL_EXTRACT_MULTI_OUT} false
assert_file_exists "backup/irepo-test/bundle/CIBUILD-bundle/"${META_FILENAME}
assert ${PULL_EXTRACT_MULTI_OUT}

echo "CASE2(3):bundle pull"
pull "" false
assert_file_exists ${META_FILENAME}
PULL_RESULT_DIR=${DEFAULT_BUNDLE_NAME}"_bundle"
assert ${PULL_RESULT_DIR}
rm -rf ${PULL_RESULT_DIR}

echo "CASE2(4):bundle pull -c"
pull "" true
PULL_RESULT_FILE_NAME=${DEFAULT_BUNDLE_NAME}"_bundle.tar.gz"
assert_file_exists ${META_FILENAME}
assert_file_exists ${PULL_RESULT_FILE_NAME}
rm ${PULL_RESULT_FILE_NAME}

echo "CASE2(5):bundle pull --out too/many/dir/out.tar.gz -c"
PULL_MULTI_OUT="backup/irepo-test/bundle/CIBUILD-bundle/r12345/output.tar.gz"
pull ${PULL_MULTI_OUT} true
assert_file_exists "backup/irepo-test/bundle/CIBUILD-bundle/r12345/"${META_FILENAME}
assert_file_exists ${PULL_MULTI_OUT}
tar -xzf ${PULL_MULTI_OUT} -C ${PULL_SINGLE_OUT} && rm ${PULL_MULTI_OUT}
assert ${PULL_SINGLE_OUT}
rm -rf ${PULL_SINGLE_OUT}/*

echo "CASE2(6):bundle pull --out too/many/dir/out.tar -c"
PULL_MULTI_OUT_TAR="backup/irepo-test/bundle/CIBUILD-bundle/tar-dir/output.tar"
pull ${PULL_MULTI_OUT_TAR} true
assert_file_exists "backup/irepo-test/bundle/CIBUILD-bundle/tar-dir/"${META_FILENAME}
assert_file_exists ${PULL_MULTI_OUT_TAR}
tar -xf ${PULL_MULTI_OUT_TAR} -C ${PULL_SINGLE_OUT} && rm ${PULL_MULTI_OUT_TAR}
assert ${PULL_SINGLE_OUT}
rm -rf ${PULL_SINGLE_OUT}/*

echo "CASE2(7):bundle pull --out exists/dir/"
PULL_EXISTS_DIR="./backup/irepo-test/"
mkdir -p ${PULL_EXISTS_DIR}
pull ${PULL_EXISTS_DIR} false
assert_file_exists "./backup/"${META_FILENAME}
assert ${PULL_EXISTS_DIR}

echo "CASE2(8):bundle pull --out exists/dir/ -c"
PULL_EXISTS_COMPRESS_DIR="./backup/irepo-test/bundle/"
PULL_EXISTS_COMPRESS_DIR_FILE_NAME=${PULL_EXISTS_COMPRESS_DIR}${DEFAULT_BUNDLE_NAME}"_bundle.tar.gz"
mkdir -p ${PULL_EXISTS_COMPRESS_DIR}
pull ${PULL_EXISTS_COMPRESS_DIR} true
assert_file_exists "./backup/irepo-test/bundle/"${META_FILENAME}
assert_file_exists ${PULL_EXISTS_COMPRESS_DIR_FILE_NAME}
tar -xzf ${PULL_EXISTS_COMPRESS_DIR_FILE_NAME} -C ${PULL_EXISTS_COMPRESS_DIR} && rm ${PULL_EXISTS_COMPRESS_DIR_FILE_NAME}
assert ${PULL_EXISTS_COMPRESS_DIR}

echo "CASE2(9):bundle pull --out ./ -c"
PULL_CURRENT_COMPRESS_DIR="./"
pull "./" true
PULL_CURRENT_COMPRESS_DIR_FILENAME=${DEFAULT_BUNDLE_NAME}"_bundle.tar.gz"
assert_file_exists ${PULL_CURRENT_COMPRESS_DIR_FILENAME}
assert_file_exists ${META_FILENAME}
mkdir -p temp2
tar -xzf ${PULL_CURRENT_COMPRESS_DIR_FILENAME} -C "temp2/"
assert "temp2"


echo "CASE2(10): bundle pull --out ./"
PULL_CURRENT_DIR="./"
pull "./" false
assert_file_exists ${META_FILENAME}
assert "./"



# 检查bundle pull构建结果
if [[ -s ${FAILED_ASSERT} ]] ;then
	cat ${FAILED_ASSERT}
	cat ${FAILED_ASSERT} | mail -s "[$PROFILE]client bundle test result" ${AGILE_TRIGGER_USER}@baidu.com
	exit -1
fi
