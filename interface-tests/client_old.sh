#!/usr/bin/env bash
# $1: 环境
# e.g.: test/online/local
# $2: Token
# e.g.: 3fba0184-a3e9-421f-8b4d-4950bb9dd855
# $3: REPO_CLI
# e.g.: output/repocli
set +e
PROFILE=$1
TOKEN=$2
REPO_CLI=$3

# 本地场景
if [ $PROFILE == 'local' ]
then
	PROFILE='test'
	REPO_CLI=../repocli
fi

# clean temp file
rm -rf result.temp
rm -rf all_command.temp
:> all_command.temp

specify_token_command_prefix="--specifyuser yangxiaonan01 --specifytoken "
specify_token_command=${specify_token_command_prefix}${TOKEN}
echo $specify_token_command
echo $specify_token_command > specify_token_command.temp

# 将token和测试case，验证条件写入文件中
cat specify_token_command.temp $PROFILE/command.txt > all_command.temp
# 从文件中读取测试case和验证条件
exec 3<"all_command.temp"
exec 4<"$PROFILE/assert.txt"
touch result.temp

while read command<&3 && read assert<&4
do
  echo $command
	echo $assert
  command_result=$($REPO_CLI $command)
  assert_result=$(echo "$command_result" | grep "$assert")
	if [[ "$assert_result" == "" ]]
	then
    echo -e "\033[31m case $command fail\033[0m"
    echo "case $command fail\n" >> result.temp
		exit -1
  fi
done

# 将结果通过邮件发送
if [ -s result.temp ] ;then
	cat result.temp | mail -s "[$PROFILE]old-client test result" <EMAIL> ${AGILE_TRIGGER_USER}@baidu.com
	exit -1
fi
