--h
--grant yangwen<PERSON>i MANAGER --spacename ai-interface-case --disable-giano
--listmembers --spacename ai-interface-case --disable-giano
--addspace ai-interface-case --disable-giano
--spacename ai-interface-case --addrepo ai-qa-test-repo --disable-giano
--spacename ai-interface-case --addprops ca5ab5befc6246a7ab4f6c4f7b28dfa5 --reponame ai-interace-case-repo --prop user_key user_value --disable-giano
--release ca5ab5befc6246a7ab4f6c4f7b28dfa5 --reponame ai-interace-case-repo --spacename ai-interface-case --version 1.0.0.1 --disable-giano
--listspace --disable-giano
--listrepo --spacename ai-interface-case --disable-giano
--listrev --reponame ai-interace-case-repo --spacename ai-interface-case --disable-giano
--revdetail ca5ab5befc6246a7ab4f6c4f7b28dfa5 --reponame ai-interace-case-repo --spacename ai-interface-case --disable-giano
--fetchrev ca5ab5befc6246a7ab4f6c4f7b28dfa5 --reponame ai-interace-case-repo --spacename ai-interface-case --downloadmode HTTP --out test --disable-giano
--fetchrev ca5ab5befc6246a7ab4f6c4f7b28dfa5 --reponame ai-interace-case-repo --spacename ai-interface-case --downloadmode HTTP --disable-giano
--specifyspace ai-interface-case --disable-giano
--specifyrepo ai-interace-case-repo --disable-giano
bundle template