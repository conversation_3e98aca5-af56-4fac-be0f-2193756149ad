# 说明:
# 此文件是client_bundle.sh的断言文件，通过校验下列文件是否存在来判断bundle build的结果是否正确
# 第一行 表示类型,f代表文件,d代表目录
# 第二行 表示文件或者目录
# model latest@ dest文件夹
d model/latest
# model @release dest文件夹
d model/release
# @release dest文件夹下的文件
f model/release/client/addmodel.py
d model/release/common
# revision dest文件夹
d model/revision
# revision 文件夹下的文件夹
d model/revision/common
# model src是文件夹
f model/revision/client/addmodel.py
f model/revision/client/__init__.py
# model 文件重命名
f model/revision/fileName.py
# zip解压
f zip/tst/Unix编程艺术.pdf
# tar解压
f tar/output/123
# tgz解压
f tgz/output/123
# 单file，非tar结尾的正常文件
f file/bundle.test.yml
# 单file存入已经存在的dir，非tar结尾的正常文件
f file-not-tar/bundle.test.yml
# 类似于tar的文件，但实际上不是tar
f file-not-tar/downloader.tar
# 类似于tar.gz的文件，但实际上不是tar.gz
f file-not-tar-gz/downloader.tar.gz
# prod release@
d prod/release
# prod release@ src是文件夹
d prod/release/remove-output/
# prod release@ src是./
d prod/release/output/testRepocli
# prod release@ src是目录 dest也是output(remove output目录)
d prod/release/remove-output/testRepocli
# 中文文件名
f chinese-files/Unix编程艺术.pdf
# bundle 依赖32-hex 目录
d bundle/revision
d bundle/revision/bin
f bundle/revision/bin/helm-cm-push
f bundle/revision/output/temp_linux.tar.gz
f bundle/revision/tekes-test/0.zip
# bundle 依赖release@ 目录
d bundle/release
f bundle/release/hello.txt
# bundle src是. dest是./
f bin/helm-cm-push
d tekes-test
# current_build@
f prod/current_build/file
d prod/current_build/dir
f prod/current_build/dir/.hidden
f bundle-debug.yml
# not rename
f not_rename/output/scripts/downloader.sh