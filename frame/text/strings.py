"""
Created on 2014-1-26
@author: <EMAIL>
@copyright: www.baidu.com
"""
import hashlib
import math
import string
import sys
import types

BACK_GROUND_BLACK = '\033[40m'
BACK_GROUND_RED = '\033[41m'
BACK_GROUND_GREEN = '\033[42m'
BACK_GROUND_YELLOW = '\033[43m'
BACK_GROUND_BLUE = '\033[44m'
BACK_GROUND_BLUE_UNDERLINE = '\033[44m'
BACK_GROUND_PURPLE = '\033[45m'
BACK_GROUND_CYAN = '\033[46m'
BACK_GROUND_WHITE = '\033[47m'
TEXT_COLOR_BLACK = '\033[30m'
TEXT_COLOR_RED = '\033[31m'
TEXT_COLOR_GREEN = '\033[32m'
TEXT_COLOR_YELLOW = '\033[33m'
TEXT_COLOR_BLUE = '\033[34m'
TEXT_COLOR_PURPLE = '\033[35m'
TEXT_COLOR_CYAN = '\033[36m'
TEXT_COLOR_WHITE = '\033[37m'
RESET = '\033[0m'
USE_SHELL = sys.platform.startswith("win")


class MD5Util(object):
    """
    md5 tools which can generate the md5 of file and text
    """

    @classmethod
    def md5_for_file(cls, f, block_size=2 ** 20):
        """calculate md5 from file
        """
        md5 = hashlib.md5()
        while True:
            data = f.read(block_size)
            if not data:
                break
            md5.update(data)
        return md5.hexdigest()

    @classmethod
    def md5_for_text(cls, content):
        """calculate md5 for content
        """
        md5 = hashlib.md5()
        md5.update(content)
        return md5.hexdigest()


class RichText(object):
    """
    rich text display
    """

    @classmethod
    def render(cls, text, bg_color, color=TEXT_COLOR_WHITE):
        """render
        """
        if USE_SHELL:
            return text
        result = []
        result.append(bg_color)
        result.append(color)
        result.append(text)
        result.append(RESET)
        return ''.join(result)

    @classmethod
    def render_text_color(cls, text, color):
        """render text with color
        """
        if USE_SHELL:
            return text
        return ''.join([color, text, RESET])

    @classmethod
    def render_green_text(cls, text):
        """render green text
        """
        return cls.render_text_color(text, TEXT_COLOR_GREEN)

    @classmethod
    def render_red_text(cls, text):
        """render red text
        """
        return cls.render_text_color(text, TEXT_COLOR_RED)

    @classmethod
    def render_blue_text(cls, text):
        """render blue text
        """
        return cls.render_text_color(text, TEXT_COLOR_BLUE)

    @classmethod
    def render_yellow_text(cls, text):
        """render yellow text
        """
        return cls.render_text_color(text, TEXT_COLOR_YELLOW)


class _StringGenerator(object):
    def __init__(self, string):
        self.string = string
        self.index = -1

    def peek(self):
        """
        peek string
        :return: None for no
        """
        i = self.index + 1
        if i < len(self.string):
            return self.string[i]
        else:
            return None

    def __next__(self):
        """
        next string
        :return:
        """
        self.index += 1
        if self.index < len(self.string):
            return self.string[self.index]
        else:
            raise StopIteration

    def all(self):
        """
        all string
        :return:
        """
        return self.string


class WriteException(Exception):
    """
    writ exception
    """
    pass


class ReadException(Exception):
    """
    read exception
    """
    pass


class JsonReader(object):
    """
    read json
    """
    hex_digits = {'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15}
    escapes = {'t': '\t', 'n': '\n', 'f': '\f', 'r': '\r', 'b': '\b'}

    def read(self, s):
        """
        read
        :param s: string
        :return: json
        """
        self._generator = _StringGenerator(s)
        result = self._read()
        return result

    def _read(self):
        self._eatWhitespace()
        peek = self._peek()
        if peek is None:
            raise ReadException("Nothing to read: '%s'"
                                % self._generator.all())
        if peek == '{':
            return self._readObject()
        elif peek == '[':
            return self._readArray()
        elif peek == '"':
            return self._readString()
        elif peek == '-' or peek.isdigit():
            return self._readNumber()
        elif peek == 't':
            return self._readTrue()
        elif peek == 'f':
            return self._readFalse()
        elif peek == 'n':
            return self._readNull()
        elif peek == '/':
            self._readComment()
            return self._read()
        else:
            raise ReadException("Input is not valid JSON: '%s'"
                                % self._generator.all())

    def _readTrue(self):
        self._assertNext('t', "true")
        self._assertNext('r', "true")
        self._assertNext('u', "true")
        self._assertNext('e', "true")
        return True

    def _readFalse(self):
        self._assertNext('f', "false")
        self._assertNext('a', "false")
        self._assertNext('l', "false")
        self._assertNext('s', "false")
        self._assertNext('e', "false")
        return False

    def _readNull(self):
        self._assertNext('n', "null")
        self._assertNext('u', "null")
        self._assertNext('l', "null")
        self._assertNext('l', "null")
        return None

    def _assertNext(self, ch, target):
        if self._next() != ch:
            raise ReadException("Trying to read %s: '%s'"
                                % (target, self._generator.all()))

    def _readNumber(self):
        isfloat = False
        result = self._next()
        peek = self._peek()
        while peek is not None and (peek.isdigit() or peek == "."):
            isfloat = isfloat or peek == "."
            result = result + self._next()
            peek = self._peek()
        try:
            if isfloat:
                return float(result)
            else:
                return int(result)
        except ValueError:
            raise ReadException("Not a valid JSON number: '%s'" % result)

    def _readString(self):
        result = ""
        assert self._next() == '"'
        try:
            while self._peek() != '"':
                ch = self._next()
                if ch == "\\":
                    ch = self._next()
                    if ch in 'brnft':
                        ch = self.escapes[ch]
                    elif ch == "u":
                        ch4096 = self._next()
                        ch256 = self._next()
                        ch16 = self._next()
                        ch1 = self._next()
                        n = 4096 * self._hexDigitToInt(ch4096)
                        n += 256 * self._hexDigitToInt(ch256)
                        n += 16 * self._hexDigitToInt(ch16)
                        n += self._hexDigitToInt(ch1)
                        ch = chr(n)
                    elif ch not in '"/\\':
                        raise ReadException("Not a valid escaped JSON"
                                            " character: '%s' in %s"
                                            % (ch, self._generator.all()))
                result = result + ch
        except StopIteration:
            raise ReadException("Not a valid JSON string: '%s'"
                                % self._generator.all())
        assert self._next() == '"'
        return result

    def _hexDigitToInt(self, ch):
        try:
            result = self.hex_digits[ch.upper()]
        except KeyError:
            try:
                result = int(ch)
            except ValueError:
                raise ReadException("The character %s is not a hex digit."
                                    % ch)
        return result

    def _readComment(self):
        assert self._next() == "/"
        second = self._next()
        if second == "/":
            self._readDoubleSolidusComment()
        elif second == '*':
            self._readCStyleComment()
        else:
            raise ReadException("Not a valid JSON comment: %s"
                                % self._generator.all())

    def _readCStyleComment(self):
        try:
            done = False
            while not done:
                ch = self._next()
                done = (ch == "*" and self._peek() == "/")
                if not done and ch == "/" and self._peek() == "*":
                    raise ReadException(
                            "Not a valid JSON comment: %s," % self._generator.all()
                            + " '/*' cannot be embedded in the comment.")
            self._next()
        except StopIteration:
            raise ReadException("Not a valid JSON comment: %s, expected */"
                                % self._generator.all())

    def _readDoubleSolidusComment(self):
        try:
            ch = self._next()
            while ch != "\r" and ch != "\n":
                ch = self._next()
        except StopIteration:
            pass

    def _readArray(self):
        result = []
        assert self._next() == '['
        done = self._peek() == ']'
        while not done:
            item = self._read()
            result.append(item)
            self._eatWhitespace()
            done = self._peek() == ']'
            if not done:
                ch = self._next()
                if ch != ",":
                    raise ReadException("Not a valid JSON array: '%s' due to: '%s'"
                                        % (self._generator.all(), ch))
        assert ']' == self._next()
        return result

    def _readObject(self):
        result = {}
        assert self._next() == '{'
        done = self._peek() == '}'
        while not done:
            key = self._read()
            if type(key) is not str:
                raise ReadException("Not a valid JSON object key (should be a string): %s" % key)
            self._eatWhitespace()
            ch = self._next()
            if ch != ":":
                raise ReadException("Not a valid JSON object: '%s' due to: '%s'"
                                    % (self._generator.all(), ch))
            self._eatWhitespace()
            val = self._read()
            result[key] = val
            self._eatWhitespace()
            done = self._peek() == '}'
            if not done:
                ch = self._next()
                if ch != ",":
                    raise ReadException("Not a valid JSON array: '%s' due to: '%s'"
                                        % (self._generator.all(), ch))
        assert self._next() == "}"
        return result

    def _eatWhitespace(self):
        p = self._peek()
        while p is not None and p in string.whitespace or p == '/':
            if p == '/':
                self._readComment()
            else:
                self._next()
            p = self._peek()

    def _peek(self):
        return self._generator.peek()

    def _next(self):
        return next(self._generator)


class JsonWriter(object):
    """
    writer for json
    """

    def _append(self, s):
        self._results.append(s)

    def write(self, obj, escaped_forward_slash=False):
        """
        write
        :param obj: json
        :param escaped_forward_slash: escape str
        :return: string
        """
        self._escaped_forward_slash = escaped_forward_slash
        self._results = []
        self._write(obj)
        return "".join(self._results)

    def _write(self, obj):
        ty = type(obj)
        if ty is dict:
            n = len(obj)
            self._append("{")
            for k, v in list(obj.items()):
                self._write(k)
                self._append(":")
                self._write(v)
                n = n - 1
                if n > 0:
                    self._append(",")
            self._append("}")
        elif ty is list or ty is tuple:
            n = len(obj)
            self._append("[")
            for item in obj:
                self._write(item)
                n = n - 1
                if n > 0:
                    self._append(",")
            self._append("]")
        elif ty is str:
            self._append('"')
        obj = obj.replace('\\', r'\\')
        if self._escaped_forward_slash:
            obj = obj.replace('/', r'\/')
            obj = obj.replace('"', r'\"')
            obj = obj.replace('\b', r'\b')
            obj = obj.replace('\f', r'\f')
            obj = obj.replace('\n', r'\n')
            obj = obj.replace('\r', r'\r')
            obj = obj.replace('\t', r'\t')
            self._append(obj)
            self._append('"')
        elif ty is int:
            self._append(str(obj))
        elif ty is float:
            self._append("%f" % obj)
        elif obj is True:
            self._append("true")
        elif obj is False:
            self._append("false")
        elif obj is None:
            self._append("null")
        else:
            raise WriteException("Cannot write in JSON: %s" % repr(obj))


ALPHABET = "bcdfghjklmnpqrstvwxyz0123456789BCDFGHJKLMNPQRSTVWXYZ"
BASE = len(ALPHABET)
MAXLEN = 6


class IdRewrite(object):
    """
    id rewrite
    """

    @classmethod
    def encode(cls, n):
        """
        decode
        :param n:
        :return:
        """
        pad = MAXLEN - 1
        n = int(n + pow(BASE, pad))
        s = []
        t = int(math.log(n, BASE))
        while True:
            bcp = int(pow(BASE, t))
            a = int(n / bcp) % BASE
            s.append(ALPHABET[a:a + 1])
            n = n - (a * bcp)
            t -= 1
            if t < 0:
                break
        return "".join(reversed(s))

    @classmethod
    def decode(cls, n):
        """
        decode
        :param n:
        :return:
        """
        n = "".join(reversed(n))
        s = 0
        l = len(n) - 1
        t = 0
        while True:
            bcpow = int(pow(BASE, l - t))
            s = s + ALPHABET.index(n[t:t + 1]) * bcpow
            t += 1
            if t > l:
                break
        pad = MAXLEN - 1
        s = int(s - pow(BASE, pad))
        return int(s)
