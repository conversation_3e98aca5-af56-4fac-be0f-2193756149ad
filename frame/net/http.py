# encoding:utf-8
"""
Created on 2014-1-26
@author: <EMAIL>
@copyright: www.baidu.com
"""

import random
import string
import http.cookiejar
import json
import logging
import mimetypes
import os
import socket
import sys
import time
import urllib.request
import urllib.parse
import urllib.error
from io import BytesIO as BIO

from common import constant
from common.conf import Config
from frame.text.console import PrintHelper

import requests
import requests_toolbelt


class URLBuilder(object):
    """URLBuilder
    """

    @classmethod
    def build_tcp(cls, host, port):
        result = []
        result.append("tcp")
        result.append("://")
        result.append(host)
        result.append(":")
        result.append(port)
        return "".join(result)

    @classmethod
    def build_http(cls, host, port):
        result = []
        result.append("http")
        result.append("://")
        result.append(host)
        result.append(":")
        result.append(port)
        return "".join(result)


class StatefullHttpService(object):
    """http service keep session on cookies
    """

    def __init__(self, cookies_file_name=None, use_old_cookies=True, logger=None,
                 print_helper=None):
        if constant.DEBUG:
            logging.basicConfig(level="DEBUG")
        self.logger = logger or logging.getLogger(__name__)
        if not cookies_file_name:
            cookies_file_name = "cookies.txt"
        self.cookies_path = os.path.join(sys.path[0], cookies_file_name)
        self.authenticated = False
        self._build_opener(use_old_cookies)
        self.pending_request = None
        self.print_helper = print_helper if print_helper else PrintHelper()

    def post(self, url, params, timeout=10000):
        """
        the parama can be a dict like {"field1":"value1","field2":"value2"}
        or be a tuple like [("field1","value1"),
                            ("field2","value2"),
                            ("field2","value2")].
        the tuple can have duplicate keys
        """
        url = self._process_url(url)
        request = self._build_request(url, params)
        return self._service(request, timeout)

    def get(self, url, timeout=1000):
        """
        get request
        """
        url = self._process_url(url)
        request = self._build_request(url)
        return self._service(request, timeout)

    def put(self, url, params=None, timeout=10000):
        """
        put request
        """
        url = self._process_url(url)
        request = self._build_request(url, params)
        request.get_method = lambda: 'PUT'
        return self._service(request, timeout)

    def patch(self, url, params=None, timeout=10000):
        """
        patch request
        """
        url = self._process_url(url)
        request = self._build_request(url, params)
        request.get_method = lambda: 'PATCH'
        return self._service(request, timeout)

    def _build_request(self, url, params=None):
        # POST
        if params:
            self.logger.debug('post %s params[%s]' % (url, params))
            request = urllib.request.Request(
                    url, json.dumps(params).encode('utf-8'),
                    {'Host': constant.HOST, 'Content-type': 'application/json'})
        # GET
        else:
            self.logger.debug('get %s params[%s]' % (url, params))
            request = urllib.request.Request(url, None, {'Host': constant.HOST})
        return request

    def _build_multipart_request(self, url, fields, files):
        content_type, body = self._encode_multipart_formdata(fields, files)
        headers = {'Content-Type': content_type,
                   'Content-Length': str(len(body))}
        r = urllib.request.Request(url, body, headers)
        return r

    def _process_url(self, url):
        url = url.strip()
        if not url.startswith("http"):
            url = "http://%s" % url
        return url

    def _build_opener(self, use_old_cookies):
        opener = urllib.request.OpenerDirector()
        opener.add_handler(urllib.request.ProxyHandler())
        opener.add_handler(urllib.request.UnknownHandler())
        opener.add_handler(urllib.request.HTTPHandler())
        opener.add_handler(urllib.request.HTTPDefaultErrorHandler())
        opener.add_handler(urllib.request.HTTPSHandler())
        opener.add_handler(urllib.request.HTTPErrorProcessor())
        self.cookie_jar = http.cookiejar.MozillaCookieJar(self.cookies_path)
        if use_old_cookies and os.path.exists(self.cookies_path):
            try:
                self.cookie_jar.load()
                self.authenticated = True
            except (http.cookiejar.LoadError, IOError):
                pass
        else:
            if os.path.exists(self.cookies_path):
                os.remove(self.cookies_path)
            fd = os.open(self.cookies_path, os.O_CREAT, 0o600)
            os.close(fd)
            # Always chmod the cookie file
            os.chmod(self.cookies_path, 0o600)
        opener.add_handler(urllib.request.HTTPCookieProcessor(self.cookie_jar))
        auth_headers = self._get_auth_headers()
        opener.addheaders = [(x, auth_headers[x]) for x in list(auth_headers.keys())]
        self.opener = opener

    def _get_auth_headers(self):
        if constant.use_giano:
            if constant.giano_credential:
                return {'GIANO-CRED': constant.giano_credential}
            try:
                import baassw
                generator = baassw.ClientUtility.Login()
                if not generator:
                    self.logger.debug("giano login fail, retry...")
                    time.sleep(3)
                    generator = baassw.ClientUtility.Login()

                if generator:
                    cred = baassw.string_p()
                    generator.GenerateCredential(cred.cast())
                    constant.giano_credential = cred.value()
                    if constant.giano_credential:
                        self.logger.debug("giano credential generate done, credential: %s",
                                          cred.value())
                        return {'GIANO-CRED': constant.giano_credential}
                    else:
                        self.logger.debug("giano credential is empty")
            except Exception as e:
                self.logger.debug("giano credential generate error", exc_info=e)

        conf = Config()
        uname = conf.get_uname()
        token = conf.get_token()
        return {'IREPO-UNAME': uname, "IREPO-TOKEN": token}

    def _service(self, request, timeout):
        old_timeout = socket.getdefaulttimeout()
        # for net condition, add retry operation
        retry_times = 0
        result = ""
        status = False
        while retry_times < 3:
            try:
                retry_times += 1
                socket.setdefaulttimeout(timeout)
                response = self.opener.open(request)
                content = response.read()
                response.close()
                self.logger.debug('content->%s,code->%d'
                                  % (content, response.code))
                if 200 <= response.code < 300:
                    result, status = content, True
                    break
                result, status = content, False
            except urllib.request.HTTPError as e:
                self.print_helper.error('HTTPError: %s' % e)
                self.print_helper.error('Auth Headers: %s' % self.opener.addheaders)
                self.logger.debug('code->%d' % e.code)
                if e.code == 302:
                    self.pending_request = (request, timeout)
                elif e.code == 403:
                    result = json.dumps({'code': 403, 'msg': 'Forbidden'})
                elif e.code == 404:
                    result = json.dumps({'code': 404, 'msg': 'Not Found'})
                elif e.code >= 400 and e.code not in [404, 403]:
                    result = e.read()
            except Exception as ex:
                result, status = str(ex), False
            finally:
                socket.setdefaulttimeout(old_timeout)
        return result, status

    def post_multipart(self, url, fields, files, timeout=1000):
        url = self._process_url(url)
        request = self._build_multipart_request(url, fields, files)
        return self._service(request, timeout)

    def get_steaming(self, url, timeout=None):
        """
        :param url: get url
        :param timeout: ms, None mean no limit
        """
        auth_headers = self._get_auth_headers()
        content, status, response = '', False, None

        self.logger.debug('get streaming %s' % url)
        try:
            response = requests.get(url, headers=auth_headers, stream=True, timeout=timeout)
            if 200 <= response.status_code < 300:
                content, status, response = '', True, response
            elif 302 == response.status_code:
                self.logger.debug('not login')
            elif 404 == response.status_code:
                content = response.content or json.dumps({'code': 404, 'msg': 'Not Found'})
            elif 403 == response.status_code:
                content = json.dumps({'code': 403, 'msg': 'Forbidden'})
            elif 400 <= response.status_code and response.status_code not in [403, 404]:
                self.logger.debug(
                        'content->%s, code->%d' % (response.content, response.status_code))
                content = response.content
        except requests.Timeout as e:
            self.logger.debug(e)
            content = str(e)
        except Exception as e:
            self.logger.debug(e)
            content = str(e)
        return content, status, response

    def post_streaming_multipart(self, url, encoder, callback=None, timeout=None):
        """
        build multipart request bodies and to avoid reading files into memory
        see <https://toolbelt.readthedocs.io/en/latest/uploading-data.html>
        """
        monitor = requests_toolbelt.MultipartEncoderMonitor(encoder, callback)
        auth_headers = self._get_auth_headers()

        headers = {'Content-Type': monitor.content_type}
        headers.update(auth_headers)
        result, status = '', False
        self.logger.debug('post streaming multipart %s' % url)
        try:
            response = requests.post(url, data=monitor, headers=headers, timeout=timeout)
            self.logger.debug('content->%s, code->%d' % (response.content, response.status_code))
            if 200 <= response.status_code < 300:
                result, status = response.content, True
            elif 302 == response.status_code:
                self.logger.debug('not login')
            elif 404 == response.status_code:
                result = response.content or json.dumps({'code': 404, 'msg': 'Not Found'})
            elif 403 == response.status_code:
                result = json.dumps({'code': 403, 'msg': 'Forbidden'})
            elif 400 <= response.status_code and response.status_code not in [403, 404]:
                result = response.content
        except requests.Timeout as e:
            self.logger.debug(e)
            result = str(e)
        except Exception as e:
            self.logger.debug(e)
            result = str(e)
        return result, status

    def _encode_multipart_formdata(self, fields, files, encode="utf-8"):
        """
        fields is a sequence of (name, value) elements for regular form fields.
        files is a sequence of (name, filename, value) elements for data
        to be uploaded as files
        Return (content_type, body) ready for httplib.HTTP instance
        """
        # BOUNDARY = mimetools.choose_boundary()
        BOUNDARY = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(30))
        CRLF = '\r\n'
        body = BIO()
        for (key, value) in fields:
            key = str(key)
            body.write(('--' + BOUNDARY).encode())
            body.write(CRLF.encode())
            body.write(('--' + BOUNDARY).encode())
            body.write(CRLF.encode())
            body.write(('Content-Disposition: form-data; name="%s"' % key).encode())
            body.write(CRLF.encode(encode))
            body.write(b'')
            body.write(CRLF.encode())
            if isinstance(value, str):
                value = value.encode(encode)
            body.write(value)
            body.write(CRLF.encode())
        for (key, filename, value) in files:
            if not value:
                continue
            key = str(key)
            filename = str(filename)
            body.write(('--' + BOUNDARY).encode())
            body.write(CRLF.encode())
            body.write(('Content-Disposition: form-data; name="%s"; filename="%s"' % (key, filename)).encode())
            body.write(CRLF.encode())
            body.write(('Content-Type: %s' % self._get_content_type(filename)).encode())
            body.write(CRLF.encode())
            body.write(b'')
            body.write(CRLF.encode())
            body.write(value.encode())
            body.write(CRLF.encode())
        body.write(('--' + BOUNDARY + '--').encode())
        body.write(CRLF.encode())
        body.write(b'')
        value = body.getvalue()
        content_type = 'multipart/form-data; boundary=%s' % BOUNDARY
        return content_type, value

    def _get_content_type(self, filename):
        try:
            return str(mimetypes.guess_type(filename)[0]) or 'application/octet-stream'
        except:
            return 'application/octet-stream'

    def handle_error_msg(self, content):
        """
        handle rest api error message
        """
        response = None
        if content:
            response = json.loads(content)
        if response:
            self.print_helper.error(
                    "[code:%s] %s" % (response.get("code"), response.get("msg")))
        else:
            self.print_helper.error("failed to access server")

    def login(self):
        raise NotImplementedError(
                "abstract method -- subclass %s must override" % self.__class__)
