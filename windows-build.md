# windows版本编译说明
## 一、基础环境准备
### 1. 需要一台windows机器（可以在百度公有云上创建）
### 2. 安装python2.7
[官网下载地址](https://www.python.org/ftp/python/2.7.18/python-2.7.18.amd64.msi)
### 3. 安装依赖
- certifi-2018.1.18
- chardet-3.0.4
- idna-2.6 
- requests-2.18.4 
- requests-toolbelt-0.8.0
- setuptools-39.0.1 
- urllib3-1.22  
可采用pip安装(安装python2.7时会自动安装pip)，参考以下命令
```shell
   pip install certifi==2018.1.18 
   pip install chardet==3.0.4 
   pip install idna==2.6 
   pip install requests==2.18.4 
   pip install requests-toolbelt==0.8.0 
   pip install setuptools==39.0.1 
   pip install urllib3==1.22 
```  
### 4. 安装打包软件pipinstaller
```shell
   pip install pyinstaller==3.0 -i https://pypi.tuna.tsinghua.edu.cn/simple/
```
## 二、执行打包
### 1. cd到repocli.py所在目录
### 2. 执行打包生成repocli.exe
```shell
   pyinstaller -F -d repocli.py
```
打包好的二进制文件会在dist目录中。  
参考：http://www.codebaoku.com/it-python/it-python-227355.html

## 三、本地验证
参考 [client](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/8VWlE4ax3_/esZ6iYrXBgkfvG) 进行一些基础功能的验证

## 四、上传二进制文件repocli.exe到bos，供用户下载
上传目录 https://bj.bcebos.com/irepo-online/artifact/online/prod/baidu/artifact/client

## 五、用户如何安装使用
### 1. 创建安装目录，比如c:\irepo
### 2. 下载客户端
   将'https://bj.bcebos.com/irepo-online/artifact/online/prod/baidu/artifact/client/********-windows/repocli.exe'下载到安装目录 
### 3.将安装目录配置到环境变量
cmd下执行
```shell
   setx /M PATH "%PATH%;c:\irepo"
```