#!/usr/bin/env python
# coding=utf-8
"""
main entrance
"""
import hashlib
import os
import sys

import client.addmodel
import client.addrepo
import client.addspace
import client.grant
import common.constant as constant
from client import version_update
from client.auth import AuthServer
from common.conf import Config
from common.parser import ArgvPaser
from frame.text.console import PrintHelper

import requests
import requests_toolbelt

print_helper = PrintHelper()
parser = ArgvPaser()


def grant(uname, level, spacename, reponame):
    """
    grant user's perm
    """
    svr = client.grant.GrantServer(print_helper)
    ret = svr.add_grant(uname, level, spacename, reponame)
    return ret


def list_members(spacename, reponame):
    """
    list members
    """
    svr = client.grant.GrantServer(print_helper)
    ret = svr.list_member(spacename, reponame)
    return ret


def add_space(space_name, save=False, comment=""):
    """
    add a space
    """
    print_helper.info("add space:" + space_name)
    svr = client.addspace.SpaceServer(print_helper)
    ret = svr.add_space(space_name, save, comment)
    return ret


def add_repo(space_name, repo_name, save=False, comment=""):
    """
    add a repo
    """
    print_helper.info(space_name + " add repo:" + repo_name)
    svr = client.addrepo.RepoServer(print_helper)
    ret = svr.add_repo(space_name, repo_name, save, comment)
    return ret


def add_model(space_name, repo_name, addr, mode, comment, version=None):
    """
    add a model
    """
    if addr.startswith('http://') or \
            addr.startswith('ftp://') or \
            addr.startswith('hdfs://') or \
            addr.startswith('afs://'):
        svr = client.addmodel.ModelServer(print_helper)
        ret = svr.add_model(space_name, repo_name, addr, comment, version=version)
    else:
        ret = add_local_model(space_name, repo_name, addr, comment, mode, version=version)
    return ret


def add_local_model(space_name, repo_name, addr, comment, mode=None, version=None):
    """
    upload model
    """
    svr = client.addmodel.ModelServer(print_helper)
    if not mode or "PORT" != mode:
        mode = "PASV"
    if not os.path.exists(addr):
        print_helper.error("not found %s in local" % addr)
        return -1
    filename = os.path.basename(addr)
    print_helper.info("filename is %s" % filename)
    if not svr.check_filename(space_name, filename):
        return -1
    sha256 = hashlib.sha256()
    file_size, read_size = os.path.getsize(addr), 0
    print_helper.info("model file size: %.2fMB" % (file_size / (1024.0 * 1024.0)))
    with open(addr, "rb") as f:
        while True:
            block = f.read(1024 * 1024 * 8)  # read 8MB each time
            if not block:
                break
            sha256.update(block)
            read_size += len(block)
            percentage = read_size * 100 / file_size
            print_helper.info(
                    "calculating model package checksum .. %d%%\r" % (
                        percentage), percentage >= 100)
            sys.stdout.flush()
        checksum = sha256.hexdigest()
    constant.file_path = addr
    print_helper.info("add model: %s, sha256: %s" % (addr, checksum))

    try:
        if requests and requests_toolbelt and "PASV" == mode:
            svr.add_local_model_pasv(space_name, repo_name, comment, addr, checksum, version)
        else:
            svr.add_local_model_port(space_name, repo_name, comment, addr, checksum, version)
    except KeyboardInterrupt as ex:
        constant.is_terminated = True
        return -1
    return 0


def add_props(space_name, repo_name, revision, props):
    """
    add properties
    """
    svr = client.addmodel.ModelServer(print_helper)
    return svr.add_props(space_name, repo_name, revision, props)


def release(space_name, repo_name, revision, version, comment):
    """
    release revision
    """
    svr = client.addmodel.ModelServer(print_helper)
    return svr.release(space_name, repo_name, revision, version, comment)


def list_space():
    """
    list space
    """
    svr = client.addspace.SpaceServer(print_helper)
    ret = svr.list_space()
    return ret


def list_repo(space_name):
    """
    list repos
    """
    svr = client.addrepo.RepoServer(print_helper)
    ret = svr.list_repo(space_name)
    return ret


def list_model(space_name, repo_name):
    """
    list models
    """
    svr = client.addmodel.ModelServer(print_helper)
    ret = svr.list_model(space_name, repo_name)
    return ret


def rev_detail(space_name, repo_name, revision):
    """
    show revision detail
    """
    svr = client.addmodel.ModelServer(print_helper)
    ret = svr.get_revision_detail(space_name, repo_name, revision)
    return ret


def fetch_rev(space_name, repo_name, revision, outpath, mode=None):
    """
    fetch specified revision
    """
    svr = client.addmodel.ModelServer(print_helper)
    if mode and "P2P" == mode:
        ret, file_path = svr.fetch_revision_p2p(space_name, repo_name, revision, outpath)
    else:
        ret, file_path = svr.fetch_revision(space_name, repo_name, revision, outpath)

    if ret == 0:
        ret = svr.check_sha256(space_name, repo_name, revision, file_path)
    return ret


def specify_default(space_name=None, repo_name=None):
    """
    set default space name and repo name
    """
    conf = Config()
    if space_name:
        conf.set_space(space_name)
        print_helper.info("set default space " + space_name)
    if repo_name:
        conf.set_repo(repo_name)
        print_helper.info("set default repo " + repo_name)
    return 0


def specify_userinfo(uname=None, token=None):
    """
    set uname and token to local conf
    """
    conf = Config()
    if uname:
        conf.set_uname(uname)
        print_helper.info("set uname " + uname)
    if token:
        conf.set_token(token)
        print_helper.info("set token " + token)
        print_helper.info("disable giano and use this user & token by --disable-giano, "
                          "otherwise repocli use giano to authenticate "
                          "with relay-login user default")
    return 0


def func_factory(options):
    """
    switch
    """
    conf = Config()

    if options.v:
        print_helper.pure_print(version_update.get_version())
        return 0
    if options.default:
        print_helper.pure_print("current\nspace name:%s\nrepo name:%s"
                                % (conf.get_space(), conf.get_repo()))
        return 0
    if options.specifyuser or options.specifytoken:
        specify_userinfo(options.specifyuser, options.specifytoken)
        return 0
    if options.specifyspace or options.specifyrepo:
        specify_default(options.specifyspace, options.specifyrepo)
        return 0

    if options.user:
        constant.uname = options.user
    if options.token:
        constant.token = options.token

    login_service = AuthServer(print_helper)
    is_login, uname = login_service.check_login()
    conf_token = conf.get_token()
    if not is_login and conf_token == '':
        print_helper.error("not login")
        print_helper.info("Please visit https://%s to obtain your CLI Token," % constant.HOST)
        print_helper.info("then execute the following command to login:")
        print_helper.info("  repocli --specifyuser <username> --specifytoken <CLI Token>")
        return -1

    if options.grant:
        if not options.spacename:
            print_helper.error("must specify spacename by --spacename")
            return -1
        if not options.grant or len(options.grant) != 2:
            print_helper.error("must specify grant [who] [what level]")
            print_helper.tip("repocli --grant robin manager")
            return -1
        return grant(
            options.grant[0], options.grant[1],
            spacename=options.spacename, reponame=options.reponame)
    if options.listmember:
        if not options.spacename:
            print_helper.error("must specify spacename by --spacename")
            return -1
        return list_members(options.spacename, options.reponame)

    if options.listspace:
        return list_space()
    if options.listrepo:
        spacename = (options.spacename if options.spacename else conf.get_space())
        if not spacename:
            print_helper.error("must specify spacename")
            return -1
        return list_repo(spacename)
    if options.listrev:
        spacename = (options.spacename if options.spacename else conf.get_space())
        reponame = (options.reponame if options.reponame else conf.get_repo())
        if not spacename or not reponame:
            print_helper.error("must specify spacename and reponame")
            return -1
        return list_model(spacename, reponame)
    if options.revdetail:
        spacename = (options.spacename if options.spacename else conf.get_space())
        reponame = (options.reponame if options.reponame else conf.get_repo())
        if not spacename or not reponame:
            print_helper.error("must specify spacename and reponame")
            return -1
        revision = options.revdetail
        return rev_detail(spacename, reponame, revision)
    if options.fetchrev:
        spacename = (options.spacename if options.spacename else conf.get_space())
        reponame = (options.reponame if options.reponame else conf.get_repo())
        if not spacename or not reponame:
            print_helper.error("must specify spacename and reponame")
            return -1
        revision = options.fetchrev
        outpath = options.out
        if constant.DOWNLOAD_P2P:
            mode = 'HTTP' if options.downloadmode and 'HTTP' == options.downloadmode else 'P2P'
        else:
            mode = 'P2P' if options.downloadmode and 'P2P' == options.downloadmode else 'HTTP'
        return fetch_rev(spacename, reponame, revision, outpath, mode)
    if options.addspace:
        comment = (options.comment if options.comment else "")
        return add_space(options.addspace, options.save, comment)
    if options.addrepo:
        comment = (options.comment if options.comment else "")
        spacename = (options.spacename if options.spacename else conf.get_space())
        if not spacename:
            print_helper.error("must specify spacename")
            return -1
        return add_repo(spacename, options.addrepo, options.save, comment)
    if options.addmodel:
        comment = (options.comment if options.comment else "")
        spacename = (options.spacename if options.spacename else conf.get_space())
        reponame = (options.reponame if options.reponame else conf.get_repo())
        if not spacename or not reponame:
            print_helper.error("must specify spacename and reponame")
            return -1
        if constant.ADD_MODEL_PASV:
            mode = 'PORT' if options.uploadmode and 'PORT' == options.uploadmode else 'PASV'
        else:
            mode = 'PASV' if options.uploadmode and 'PASV' == options.uploadmode else 'PORT'

        version = options.version if options.version else None

        return add_model(spacename, reponame, options.addmodel, mode, comment, version)
    if options.addprops:
        if not options.props:
            print_helper.error("must specify property key and value by --prop")
            return -1

        spacename = options.spacename or conf.get_space()
        reponame = options.reponame or conf.get_repo()
        if not spacename or not reponame:
            print_helper.error("must specify spacename and reponame")
            return -1

        props_dict = dict()
        for prop in options.props:
            if prop[0]:
                props_dict[prop[0]] = prop[1]

        return add_props(spacename, reponame, options.addprops, props_dict)

    if options.release:
        spacename = options.spacename or conf.get_space()
        reponame = options.reponame or conf.get_repo()
        if not spacename or not reponame:
            print_helper.error("must specify spacename and reponame")
            return -1
        if not options.version:
            print_helper.error("must specify version with --version")
            return -1
        revision = options.release
        version = options.version
        comment = options.comment or ''

        return release(spacename, reponame, revision, version, comment)
    # specifyuser and specifytoken can use with any other command
    # if no other command will check whether specify commad or not
    if options.specifyuser or options.specifytoken:
        return 0
    parser.get_main_parser().print_help()
    return -1


def _adapt_https_ca():
    try:
        import ssl
    except ImportError:
        pass
    else:
        # LibreSSL is a fork of OpenSSL and supports SHA256
        if ssl.OPENSSL_VERSION.startswith('LibreSSL '):
            return

        if not ssl.OPENSSL_VERSION.startswith('OpenSSL '):
            pass

        try:
            lowest_version = [0, 9, '8o']
            cur_ssl_version = ssl.OPENSSL_VERSION.replace('OpenSSL ', '').split('.')

            support_sha256 = True

            for i in range(0, min(len(lowest_version), len(cur_ssl_version))):
                try:
                    if int(lowest_version[i]) > int(cur_ssl_version[i]):
                        support_sha256 = False
                        break
                    elif int(lowest_version[i]) == int(cur_ssl_version[i]):
                        continue
                    else:
                        support_sha256 = True
                        break
                except ValueError:
                    # 如果无法转换为整数，假设版本号足够新
                    support_sha256 = True
                    break
        except Exception:
            support_sha256 = True

        # 0.9.8o+ OpenSSL has SHA256 digest algorithm
        if not support_sha256:
            try:
                _create_unverified_https_context = ssl._create_unverified_context
                ssl._create_default_https_context = _create_unverified_https_context
            except AttributeError:
                # Legacy Python that doesn't verify HTTPS certificates by default
                pass
            except Exception as e:
                print(f"Error initializing SSL context: {e}")


def _try_init_giano(options):
    if options.disable_giano or options.token:
        constant.use_giano = False
    if not constant.use_giano:
        return
    sys.path.append(os.path.join(sys.path[0], "lib"))
    try:
        import baassw
    except ImportError:
        constant.use_giano = False
        return

    baassw.BAAS_Init()


def main():
    """
    main func
    """
    try:
        options = parser.get_main_parser().parse_args()
        _try_init_giano(options)
        _adapt_https_ca()
        res = func_factory(options)
        sys.exit(res)
    except KeyboardInterrupt as ex:
        if constant.DEBUG:
            print_helper.error(ex)
        else:
            print_helper.info("operation cancelled")
        sys.exit(-1)


if __name__ == "__main__":
    main()
