# -* encoding:utf-8 -*-
"""
模型相关操作
"""
import hashlib
import json
import os
import re
import subprocess
import sys
import time
import threading
import urllib.request
import urllib.parse
import urllib.error

from common import constant
from common import version_utils
from common.bns import BnsParser
from frame.net.http import StatefullHttpService
from server import filesvr

import requests_toolbelt
from requests_toolbelt.downloadutils import tee
from requests_toolbelt.downloadutils import stream

BYTES_PER_MB = 1024.0 * 1024.0


class ModelServer(StatefullHttpService):
    """
    model 操作封装
    """

    def __init__(self, print_helper=None):
        super(ModelServer, self).__init__(print_helper=print_helper)
        self.print_helper = print_helper
        bns_parser = BnsParser()
        self.check_filename_url = bns_parser.get_url(constant.CHECK_FILENAME_URL)
        self.add_model_url = bns_parser.get_url(constant.ADD_MODEL_URL)
        self.get_progress_url = bns_parser.get_url(constant.GET_PROGRESS_URL)
        self.get_revision_detail_url = bns_parser.get_url(constant.GET_REVISION_DETAIL_URL)
        self.get_revision_file_url = bns_parser.get_url(constant.GET_REVISION_FILE_URL)
        self.mk_gko3_seed_url = bns_parser.get_url(constant.MK_GKO3_SEED_URL)
        self.list_model_url = bns_parser.get_url(constant.LIST_MODEL_URL)
        self.add_props_url = bns_parser.get_url(constant.ADD_PROPS_URL)
        self.add_release_url = bns_parser.get_url(constant.ADD_RELEASE_URL)
        self.add_sha256_url = bns_parser.get_url(constant.GET_SHA256_URL)
        self.content_list = []

    def status_text(self, status):
        """convert status code to text"""
        status = int(status)
        status_text = 'UNKNOWN'
        if status == 0:
            status_text = 'INITIAL'
        elif status == 1:
            status_text = 'UPLOADING'
        elif status == 2:
            status_text = 'FAILED'
        elif status == 3:
            status_text = 'READY'
        return status_text

    def list_model(self, space_name, repo_name):
        """
        list space's repo's models
        """
        url = self.list_model_url % (space_name, repo_name)
        content, status = self.get(url, 10000)
        if status:
            nodes = json.loads(content)
            if not nodes or len(nodes) == 0:
                self.print_helper.info("repo is empty")
            for node in nodes:
                self.print_helper.pure_print(
                    "id: %-5d\trevision: %-40s\tcreator: %s\tcreate time: %s\tcomment: %s"
                    % (node.get('id'), node.get('revision'), node.get('createdBy'),
                       node.get('created'), node.get('comment')), encode='utf-8')
            return 0
        else:
            self.handle_error_msg(content)
        return -1

    def add_model(self, space_name, repo_name, address, comment, checksum=None, version=None):
        """
        Push model to reposervice
        """
        self.print_helper.info("connect to server")
        self.print_helper.info("upload mode: PORT")
        request_param = [
            ("sourceUri", address),
            ("comment", comment)
        ]
        if checksum:
            request_param.append(("sha256", checksum))

        if version_utils.is_three_bit_version(version):
            request_param.append(("threeBitVersion", version))
        elif version_utils.is_four_bit_version(version):
            request_param.append(("fourBitVersion", version))
        url = self.add_model_url % (space_name, repo_name)
        content, status = self.post_multipart(url, request_param, [], timeout=10000)
        if status:
            node = json.loads(content)
            if node:
                self.print_helper.info("success add model")
                self.print_helper.info("id: %s" % node.get("id"))
                self.print_helper.info("revision: %s" % node.get('revision'))
                self.print_helper.info("async transfer task is running, "
                                       "you can check status by: repocli --revdetail %s"
                                       % node.get('revision'))
                return 0
        else:
            self.handle_error_msg(content)
        return -1

    def add_local_model_pasv(self, space_name, repo_name, comment, address, sha256, version=None):
        """
        upload local model in mode pasv
        """
        self.print_helper.info("connect to server")
        self.print_helper.info("upload mode: PASV")
        url = self.add_model_url % (space_name, repo_name)

        def callback(monitor):
            """
            callback when post streaming multipart
            :param monitor: :class:`requests_toolbelt.MultipartEncoderMonitor`
            """
            if not hasattr(monitor, 'last_refresh_timestamp'):
                monitor.last_refresh_timestamp = time.time()

            bytes_read = monitor.bytes_read
            encoder = monitor.encoder
            percentage = bytes_read * 100 / encoder.len
            REFRESH_INTERVAL_SECONDS = 1
            if time.time() - monitor.last_refresh_timestamp >= REFRESH_INTERVAL_SECONDS \
                    or percentage >= 100:
                mb_read = bytes_read / BYTES_PER_MB
                mb_file = encoder.len / BYTES_PER_MB
                self.print_helper.info(
                    "uploading model ... %d%% %.2fMB/%.2fMB\r" % (percentage, mb_read, mb_file),
                    percentage >= 100)
                # 强制刷新标准输出缓存区
                sys.stdout.flush()
                monitor.last_refresh_timestamp = time.time()

        filename = os.path.basename(address)
        if not filename:
            self.print_helper.error("get filename from %s failed" % address)
            return -1
        request_param = {
            'comment': comment,
            'sha256': sha256,
            'file': (urllib.parse.quote(filename), open(address, 'rb'), 'text/plain')
        }

        if version_utils.is_three_bit_version(version):
            request_param['threeBitVersion'] = version
        elif version_utils.is_four_bit_version(version):
            request_param['fourBitVersion'] = version
        data = requests_toolbelt.MultipartEncoder(request_param)
        content, status = self.post_streaming_multipart(url, data, callback)
        if status:
            node = json.loads(content)
            if node:
                self.print_helper.info("success add model")
                self.print_helper.info("id: %s" % node.get('id'))
                self.print_helper.info("revision: %s" % node.get('revision'))
                return 0
        else:
            self.handle_error_msg(content)
        return -1

    def add_local_model_port(self, space_name, repo_name, comment, addr, checksum, version=None):
        """
        upload local model in mode port
        """
        constant.file_path = addr
        con = threading.Condition()
        server = filesvr.Server(self.print_helper, con)
        con.acquire()
        try:
            thread = threading.Thread(name='FileUploadServer', target=server.start_server)
            thread.start()
            self.print_helper.info(
                "start FileUploadServer, %s:%s" % (server.server_name, server.server_port))
            url = "http://%s:%s/upload/%s" % (
                server.server_name, server.server_port, os.path.basename(addr))
            resp = self.add_model(space_name, repo_name, url, comment, checksum, version)
            if resp != 0:
                server.stop_server()
                return -1
            con.wait()
            con.release()
            server.stop_server()
            return resp
        except KeyboardInterrupt as ex:
            self.print_helper.error("interrupt by user")
            constant.is_terminated = True
            server.stop_server()
            return -1

    def get_revision_detail(self, space_name, repo_name, revision):
        """
        get rev detail
        """
        url = self.get_revision_detail_url % (space_name, repo_name, revision)
        content, status = self.get(url, 10000)
        if status:
            node = json.loads(content)
            if node:
                self.print_helper.info("id: %s" % node.get('id'))
                self.print_helper.info("revision: %s" % node.get('revision'))
                self.print_helper.info("comment: %s" % node.get('comment'))
                self.print_helper.info("creator: %s" % node.get('createdBy'))
                self.print_helper.info("create time: %s" % node.get('created'))
                self.print_helper.info("transfer status: %s" % node.get('status'))
                return 0
        else:
            self.handle_error_msg(content)
        return -1

    def fetch_revision(self, space_name, repo_name, revision, outpath):
        """
        Fetch model file by http
        """
        self.print_helper.info("download revision: %s" % revision)
        url = self.get_revision_file_url % (space_name, repo_name, revision)
        text, status, response, = self.get_steaming(url)
        if status:
            file_path = stream.get_download_file_path(response, outpath)
            self.print_helper.info("save path: %s" % file_path, encode='utf-8')

            bytes_file = int(response.headers.get('Content-Length', 0))
            mb_file = bytes_file / BYTES_PER_MB
            bytes_saved = 0

            with open(file_path, 'wb') as save_file:
                for chunk in tee.tee(response, save_file, chunksize=1024 * 1024 * 8):
                    bytes_saved += len(chunk)
                    if bytes_file:
                        percentage = bytes_saved * 100 / bytes_file
                        mb_saved = bytes_saved / BYTES_PER_MB
                        self.print_helper.info("saving model ... %d%% %.2fMB/%.2fMB\r" % (
                            percentage, mb_saved, mb_file), percentage >= 100)

            self.print_helper.info("success download model")
            return 0, file_path
        else:
            self.handle_error_msg(text)
        return -1, None

    def fetch_revision_p2p(self, space_name, repo_name, revision, outpath):
        """
        Fetch model file by p2p
        """
        if not outpath:
            outpath = '%s.tar' % revision
        elif os.path.isdir(outpath):
            outpath += '/%s.tar' % revision
        self.print_helper.info("download revision: %s" % revision)
        self.print_helper.info("save path: %s" % outpath)
        url = self.mk_gko3_seed_url % (space_name, repo_name, revision)

        self.print_helper.info("mkseed for revision: %s" % revision)

        con = threading.Condition()
        con.acquire()
        server = filesvr.Server(self.print_helper, con)
        try:
            thread = threading.Thread(name='FileUploadServer', target=server.start_server)
            thread.start()

            self.print_helper.info(
                "start CallBackServer, %s:%s" % (server.server_name, server.server_port))
            callback_url = "http://%s:%s/gko3/start" % (server.server_name, server.server_port)

            result, status = self.post(url, {"callback": callback_url})
            if not status:
                self.handle_error_msg(result)
                server.stop_server()
                constant.is_terminated = True
                return -1, None
            elif result != "NONE":
                constant.gko3_seed = result
            else:
                con.wait(timeout=constant.TIMEOUT)
            con.release()
            server.stop_server()
            if not constant.gko3_seed or "NONE" == constant.gko3_seed:
                self.print_helper.error("make seed failed!")
                constant.is_terminated = True
                return -1, None
            seed_info_hash = constant.gko3_seed
            process = subprocess.Popen(
                ["gko3", "down", '-d', '100', '-S', '0', '-u', '50', '--auto-add-time', '3600',
                 "-i", seed_info_hash, '-n', outpath],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE)
            pat_log = re.compile('progress:\s*([^ ,]+),.*downrate:\s*([^ ,]+)')
            pat_warn = re.compile('^\[FATAL\](.*)')
            while process.poll() is None:
                proc_output = process.stderr.readline().decode('utf-8')
                m = pat_log.search(proc_output)
                if m is not None:
                    self.print_helper.info("progress: %s, speed: %s" % (m.group(1), m.group(2)))
                else:
                    m = pat_warn.search(proc_output)
                    if m is not None:
                        self.print_helper.error(m.group(1))
            proc_ret = process.wait()
            if proc_ret == 0:
                return 0, outpath
            else:
                print ("failed to fetch revision")
                return -1, None
        except KeyboardInterrupt as _:
            self.print_helper.error("interrupt by user")
            constant.is_terminated = True
            server.stop_server()
            return -1, None

    def inquire_status(self, space_name, repo_name, node_id):
        """
        获取进度
        """
        # get current progress
        url = self.get_revision_detail_url % (space_name, repo_name, node_id)
        content, status = self.get(url, 10000)
        if status:
            node = json.loads(content)
            if node:
                status = node.get('status')
                if status == 'FAIL':
                    return True, False
                elif status == 'SUCCESS':
                    return True, True
                return False, None
        else:
            self.print_helper.error("failed to access server")
            return False, None

    def add_props(self, space_name, repo_name, revision, props):
        """
        add properties
        """
        self.print_helper.info("space name: %s" % space_name)
        self.print_helper.info("repo name: %s" % repo_name)
        self.print_helper.info("revision: %s" % revision)
        self.print_helper.info("add props: %s" % props)

        url = self.add_props_url % (space_name, repo_name, revision)
        content, status = self.patch(url, props)
        if status:
            self.print_helper.info("add props success")
            return 0
        else:
            self.handle_error_msg(content)
            return -1

    def release(self, space_name, repo_name, revision, version, comment):
        """
        release revision
        """
        self.print_helper.info("space name: %s" % space_name)
        self.print_helper.info("repo name: %s" % repo_name)
        self.print_helper.info("revision: %s" % revision)
        self.print_helper.info("release version: %s" % version)

        url = self.add_release_url % (space_name, repo_name)
        params = {
            "revision": revision,
            "description": comment
        }
        if version_utils.is_three_bit_version(version):
            params['threeBitVersion'] = version
        elif version_utils.is_four_bit_version(version):
            params['fourBitVersion'] = version
        else:
            self.print_helper.error("version should like ******* or 1.0.1")
            return -1
        content, status = self.post(url, params)
        if status:
            promotion = json.loads(content)
            if promotion:
                self.print_helper.info("release success")
                self.print_helper.info(
                    "release four bit version: %s" % promotion.get("fourBitVersion"))
                return 0
        else:
            self.handle_error_msg(content)
        return -1

    def check_sha256(self, space_name, repo_name, revision, file_path):
        """
        check file sha256 with sha256 in repo server
        """
        url = self.add_sha256_url % (space_name, repo_name, revision)

        re_sha256, status = self.get(url)
        if not status:
            self.handle_error_msg(re_sha256)
            return -1
        sha256 = hashlib.sha256()
        file_size, read_size = os.path.getsize(file_path), 0
        self.print_helper.info("model file size: %.2fMB" % (file_size / (1024.0 * 1024.0)))
        with open(file_path, "rb") as f:
            while True:
                block = f.read(1024 * 1024 * 8)  # read 8MB each time
                if not block:
                    break
                sha256.update(block)
                read_size += len(block)
                percentage = read_size * 100 / file_size
                self.print_helper.info("calculating model package checksum .. %d%%\r" % percentage,
                                       percentage >= 100)
                sys.stdout.flush()
            checksum = sha256.hexdigest()
        if str(checksum) == str(re_sha256):
            return 0
        return -1

    def check_filename(self, space_name, filename):
        """
        check filename
        """
        url = self.check_filename_url % space_name
        context, status = self.post(url, {"filename": filename})
        if not status:
            self.handle_error_msg(context)
            return False
        return True
