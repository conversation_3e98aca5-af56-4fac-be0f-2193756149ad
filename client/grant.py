# -* encoding:utf-8 -*-
"""
授权相关操作
"""
import json

from common import constant
from common.bns import BnsParser
from frame.net.http import StatefullHttpService


class GrantServer(StatefullHttpService):
    """
    grant 操作封装
    """
    def __init__(self, print_helper):
        super(GrantServer, self).__init__()
        self.print_helper = print_helper
        bns_parser = BnsParser()
        self.grant_space_url = bns_parser.get_url(constant.GRANT_SPACE_URL)
        self.grant_repo_url = bns_parser.get_url(constant.GRANT_REPO_URL)
        self.list_space_member_url = bns_parser.get_url(constant.LIST_SPACE_MEMBER_URL)
        self.list_repo_member_url = bns_parser.get_url(constant.LIST_REPO_MEMBER_URL)

    def add_grant(self, uname, level, spacename, reponame):
        """
        grant a user perm
        """
        perm_list = ("MANAGER", "WRITER", "READER", "NONE")
        if level not in perm_list:
            self.print_helper.error("no such permission, MANAGER/WRITER/READER/NONE are supported")
            return -1
        url = self.grant_space_url % (spacename, uname, level)
        if reponame:
            url = self.grant_repo_url % (spacename, reponame, uname, level)

        content, status = self.put(url, 10000)
        if status:
            self.print_helper.info("success grant %s permission %s" % (uname, level))
            return 0
        else:
            self.handle_error_msg(content)
        return -1

    def list_member(self, spacename, reponame):
        """
        list space or repo member
        """
        url = self.list_space_member_url % spacename
        if reponame:
            url = self.list_repo_member_url % (spacename, reponame)
        content, status = self.get(url, 10000)
        if status:
            members = json.loads(content)
            if members:
                member_dict = dict()
                for member in members:
                    if not member.get('role') or not member.get('name'):
                        continue
                    if member['role'] not in member_dict:
                        member_dict[member['role']] = []
                    member_dict[member['role']].append(member['name'])

                for role in member_dict:
                    self.print_helper.pure_print("%s\t: " % role, False)
                    first = True
                    for member in member_dict[role]:
                        if not first:
                            self.print_helper.pure_print(",", False)
                        self.print_helper.pure_print("%s" % member, False)
                        first = False
                    self.print_helper.pure_print("", True)
                return 0
        else:
            self.handle_error_msg(content)
        return -1
