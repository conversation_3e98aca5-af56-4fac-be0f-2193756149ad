# -*- coding: UTF-8 -*-
"""
update cli
"""
import json
import os
import re

import sys

from buildin.cli import request_tools, local_tools, archive
from buildin.cli.console import Log
from common.conf import Config

VERSION_INFO_URL = request_tools.protocol_and_host + "/rest/v1/client/version/python3"


def get_version():
    """ 获取版本号 """
    ver_file = os.path.join(sys.path[0], "version")
    if not os.path.exists(ver_file):
        return 'unknown'
    with open(ver_file, 'r') as file_obj:
        version = file_obj.read()
        return version.strip()


def check_for_update():
    """ 检查是否需要自动更新 """
    # 如果有.git目录，表明当前客户端是在开发路径，不检查更新
    git_dir = os.path.join(sys.path[0], ".git")
    if os.path.exists(git_dir):
        return False

    # 如果配置了不检查更新，则不更新
    if not Config().get_is_auto_update():
        return False

    ver_file = os.path.join(sys.path[0], "version")
    # 若版本信息文件不存在，则更新
    if not os.path.exists(ver_file):
        return True
    with open(ver_file) as file_obj:
        cur_ver = file_obj.read().strip()
    # 若版本信息文件为空，则更新
    if not cur_ver:
        return True
    # 若版本信息内容不符合预期，则更新
    if not re.match('^(\d+\.){3}\d+$', cur_ver):
        return True
    try:
        response = request_tools.do_get(VERSION_INFO_URL)
        request_tools.check_response(response)
    except Exception as e:
        Log.debug("check for update error: %s" % str(e))
        # 若访问版本信息接口失败，则不更新
        return False

    new_ver_info = json.loads(response.content)
    if new_ver_info and new_ver_info.get("version"):
        latest_ver = new_ver_info.get("version")
    else:
        # 若返回的数据结构不符合预期，则不更新
        return False
    # 若返回的version不符合预期，则不更新
    if not re.match('^(\d+\.){3}\d+$', latest_ver):
        return False

    if cur_ver != latest_ver:
        return True
    return False


def update():
    """ 更新client """
    Log.info("client need update")
    response = request_tools.do_get(VERSION_INFO_URL)
    request_tools.check_response(response)
    version_info = json.loads(response.content)
    product_http_url = version_info.get("productHttpPath")
    local_tools.init_temp_workspace()
    temp_tgz_file = os.path.join(local_tools.TEMP_WORKSPACE_ROOT, "output.tar.gz")
    response = request_tools.do_get(product_http_url, params=None, stream=True)
    try:
        request_tools.check_response(response)
    except Exception as e:
        Log.debug(e)
        Log.warn("request bos url %s failed, please hi langshiquan" % product_http_url)
        return
    request_tools.write_to_file(response, temp_tgz_file)
    archive.TargzArchive.extract(temp_tgz_file)
    """ 
    WORKSPACE_ROOT +- output +- install +- install.sh
                   |
                   +- ...
    """
    shell_file = os.path.join(os.path.dirname(temp_tgz_file), "output", "install", "install.sh")
    if 'darwin' == sys.platform:
        Log.info("need sudo")
        status = os.system("sudo bash " + shell_file + " -f -t" + sys.path[0])
    else:
        status = os.system("bash " + shell_file + " -f -t" + sys.path[0])
    if status is 0:
        Log.info("client update finished, please redo command")
        local_tools.clean_tmp_workspace()
        sys.exit(0)
    else:
        Log.error("client update failed, please hi langshiquan")
        local_tools.clean_tmp_workspace()
