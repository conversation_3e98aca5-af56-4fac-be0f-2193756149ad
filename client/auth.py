# -* encoding:utf-8 -*-
"""
login
"""
from frame.net.http import StatefullHttpService
from frame.text import console
from common.constant import LOGIN_URL
from common.constant import USER_INFO_URL
from common.constant import CHECK_LOGIN_URL
from common.bns import BnsParser
import socket
import urllib.error
import json

class AuthServer(StatefullHttpService):
    """
    auth server
    """
    def __init__(self, print_helper):
        super(AuthServer, self).__init__()
        self.print_helper = print_helper
        bns_parser = BnsParser()
        self.check_login_url = bns_parser.get_url(CHECK_LOGIN_URL)

    def check_login(self):
        """
        check is login
        return flag and user name
        """
        request_param = {}
        content, status = self.post(self.check_login_url, request_param, 10000)
        if status:
            content_obj = json.loads(content)
            if 'username' not in content_obj or not content_obj['username']:
                self.print_helper.error("you need check your login status")
                return False, ""
            else:
                uname = content_obj['username']
                self.print_helper.pure_print(f"login as {uname}")
                return True, uname
        else:
            self.print_helper.error("cannot check login status")
            self.print_helper.pure_print(f"check_login_url: {self.check_login_url}")
            return False, ""


class LoginFailException(Exception):
    """
    LoginFailException
    """
    pass

class CanLoginCooderHttpService(StatefullHttpService):
    """
    log in uuap and save cookie service
    """
    def __init__(self, print_helper=None, use_old_cookies=True):
        super(CanLoginCooderHttpService, self).__init__()
        StatefullHttpService.__init__(self, use_old_cookies=use_old_cookies)
        self.print_helper = print_helper or console.PrintHelper()
        self.cache_user = None
        bns_parser = BnsParser()
        self.current_user_url = bns_parser.get_url(USER_INFO_URL)
        self.login_url = bns_parser.get_url(LOGIN_URL)
        self.user = None
        self.password = None

    def login(self, account=None, password=None):
        """do log in
        :param account: str, email prefix
        :param password: str, email password
        :return:
        """
        if self.user and self.password:
            account = self.user
            password = self.password
        else:
            self.print_helper.pure_print("Please login uuap")
            self.print_helper.tip("the account is the email prefix "
                                  "and password is email password")
            account = account or console.promot_input(
                "username:",
                validate_func=console.not_none_or_empty,
                try_times=2)
            password = password or console.promot_password_input(
                "password:",
                validate_func=console.not_none_or_empty,
                try_times=2)
        request_para = {
            "username": account,
            "password": password,
            "source": "upload_script"
        }
        request = self._build_request(self.login_url, request_para)
        content, status = self.login_service(request, 10000)
        if status:
            if self.pending_request:
                return self._service(*self.pending_request)
            else:
                return "", True
        else:
            raise LoginFailException("fail to login uuap: %s\n" % content)

    def login_service(self, request, timeout):
        """
        log in service
        :param request: log in request
        :param timeout: wait time
        :return:
        """
        old_timeout = socket.getdefaulttimeout()
        socket.setdefaulttimeout(timeout)
        try:
            response = self.opener.open(request)
            content = response.read()
            response.close()
            self.logger.debug('content: %s, code: %d'
                              % (content, response.code))
            if response.code in [200, 201, 202]:
                return content, True
            return content, False
        except urllib.error.HTTPError as e:
            if e.code in [302]:
                self.cookie_jar.extract_cookies(e, request)
                self.cookie_jar.save()
                return None, True
            else:
                return e.read(), False
        except Exception as ex:
            return str(ex), False
        finally:
            socket.setdefaulttimeout(old_timeout)

    def current_user(self):
        """get current user
        """
        content, status = self.get(self.current_user_url)
        if self.cache_user:
            return self.cache_user
        if status and content:
            content_obj = json.loads(content)
            if content_obj and content_obj['uname']:
                self.cache_user = content_obj['uname']
                return self.cache_user
        raise LoginFailException("fail to login uuap %s\n" % content)

    def set_user_password(self, user, password):
        """specify user and password
        :param user: svn username
        :param password: svn password
        :return: None
        """
        self.user = user
        self.password = password
