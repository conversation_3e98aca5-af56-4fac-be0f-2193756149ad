# -* encoding:utf-8 -*-
"""
repo client
"""
from frame.net.http import StatefullHttpService
from frame.text import console
from common.constant import ADD_REPO_URL
from common.constant import LIST_REPO_URL
from common.bns import BnsParser
import socket
import json
from common.conf import Config


class RepoServer(StatefullHttpService):
    """
    repo 操作封装
    """

    def __init__(self, print_helper=None):
        super(RepoServer, self).__init__()
        self.print_helper = print_helper
        bns_parser = BnsParser()
        self.add_repo_url = bns_parser.get_url(ADD_REPO_URL)
        self.list_repo_url = bns_parser.get_url(LIST_REPO_URL)
        self.conf = Config()

    def add_repo(self, space_name, repo_name, save=False, comment=""):
        """
        add a repo
        """
        self.print_helper.info("connect to server")
        request_param = {
            "space": space_name,
            "name": repo_name,
            "description": comment
        }
        url = self.add_repo_url % space_name
        content, status = self.post(url, request_param, 10000)
        if status:
            repo = json.loads(content)
            if repo:
                self.print_helper.info("success add repo:" + repo_name)
                if save:
                    self.conf.set_space(space_name)
                    self.conf.set_repo(repo_name)
                return 0
        else:
            self.handle_error_msg(content)
        return -1

    def list_repo(self, space_name):
        """
        list a space's repo
        """
        url = self.list_repo_url % space_name
        content, status = self.get(url, 10000)
        perm_dict = {'0': "no auth", '1': "RO", '2': "RW", '3': 'manager'}
        if status:
            repos = json.loads(content)
            if not repos:
                self.print_helper.info("space is empty")
                return 0
            for repo in repos:
                self.print_helper.pure_print(
                    "name: %-20s\tpermission: %-7s\tcreator: %-20s\tdescription: %s" % (
                        repo.get('name'),
                        repo.get('permissionRole'),
                        repo.get('createdBy'),
                        repo.get('description')), encode='utf-8')
            return 0
        else:
            self.handle_error_msg(content)
        return -1
