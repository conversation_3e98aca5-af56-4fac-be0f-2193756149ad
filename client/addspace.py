# -* encoding:utf-8 -*-
"""
repo space
"""
import json
from frame.net.http import StatefullHttpService
from common.constant import ADD_SPACE_URL
from common.constant import LIST_SPACE_URL
from common.bns import BnsParser
from common.conf import Config


class SpaceServer(StatefullHttpService):
    """
    repo space 相关操作封装
    """

    def __init__(self, print_helper=None):
        super(SpaceServer, self).__init__()
        self.print_helper = print_helper
        bns_parser = BnsParser()
        self.add_space_url = bns_parser.get_url(ADD_SPACE_URL)
        self.list_space_url = bns_parser.get_url(LIST_SPACE_URL)
        self.conf = Config()

    def add_space(self, space_name, save=False, comment=""):
        """
        add a space
        """
        self.print_helper.info("connect to server")
        request_param = {
            "name": space_name,
            "description": comment
        }
        content, status = self.post(self.add_space_url, request_param, 10000)
        if status:
            space = json.loads(content)
            if space:
                self.print_helper.info("success add space:" + space_name)
                if save:
                    self.conf.set_space(space_name)
                    self.conf.set_repo("")
                return 0
        else:
            self.handle_error_msg(content)
        return -1

    def list_space(self):
        """
        list one user's space
        """
        url = self.list_space_url
        content, status = self.get(url, 10000)
        perm_dict = {'0': "no auth", '1': "RO", '2': "RW", '3': 'manager'}
        if status:
            spaces = json.loads(content)
            if len(spaces) == 0:
                self.print_helper.info("space is empty")
            for space in spaces:
                self.print_helper.pure_print(
                    ("name: %-20s\tpermission: %-7s\t" +
                     "creator: %-15s\tdescription: %s") % (
                        space.get('name'),
                        space.get('permissionRole'),
                        space.get('createdBy'),
                        space.get('description')), encode='utf-8')
            return 0
        else:
            self.handle_error_msg(content)
        return -1
