# -*- coding:utf-8 -*-
"""executor for command provider"""
import os

from buildin.cli import local_tools, console, giano
from buildin.cli.exception import IRepoException
from buildin.cli.options import GlobalOptions


class IRepoExecutor(object):
    """interface of executor"""

    def __init__(self, args_processor_clz, executor_class_factory=None):
        self.executor_class_factory = executor_class_factory
        self.argv = None
        # set different processor
        if args_processor_clz and not isinstance(args_processor_clz(), IRepoArgsProcessor):
            raise Exception('bad processor!')
        self.args_processor_clazz = args_processor_clz

    def invoke(self, options):
        """func of argpaser"""
        if self.args_processor_clazz is not None:
            processor = self.args_processor_clazz(options)
            processor.process_rec()
            self.argv = processor.options
        else:
            self.argv = options
        self._do()

    def _do(self):
        pass


class IRepoArgsProcessor(object):
    """argument deep processor"""

    def __init__(self, options=None):
        self.options = options
        if self.options is None:
            self.options = dict()

    def process_rec(self):
        """recursive process, do all ancestors process"""
        if len(self.__class__.mro()) > 2:
            self.__class__.mro()[1](self.options).process_rec()
        self._process()

    def _process(self):
        pass


class RootArgsProcessor(IRepoArgsProcessor):
    """ root args processor """

    def _process(self):
        self.__process_name()
        self.__process_auth()

    def __process_name(self):
        # module[:tag][@rev]
        if self.options.get('name'):
            if '@' in self.options['name']:
                self.options['module'] = self.options['name'].split('@')[0]
                self.options['resource'] = self.options['name'].split('@')[1]
                self.options['resource_type'] = 'nodes'
            elif ':' in self.options['name']:
                self.options['module'] = self.options['name'].split(':')[0]
                self.options['resource'] = self.options['name'].split(':')[1]
                self.options['resource_type'] = 'releases'
            else:
                self.options['module'] = self.options['name']
        else:
            self.options['module'] = local_tools.get_git_repo_name()

    def __process_auth(self):
        # 获取用户输入的user、token
        if not GlobalOptions.user:
            GlobalOptions.user = self.options.get('user')
        if not GlobalOptions.token:
            GlobalOptions.token = self.options.get('token')
        # 不能只写user
        if GlobalOptions.user and not GlobalOptions.token:
            raise IRepoException("--user must be used with --token")
        # 如果用户输入了token，则使用用户输入的token
        if GlobalOptions.token:
            GlobalOptions.disable_giano = True
            return
        # 如果环境变量中存在token，则获取环境变量中的token
        if os.environ.get('IREPO_JWT'):
            GlobalOptions.token_in_env = os.environ.get('IREPO_JWT')
            return
        # 如果用户使用门神，尝试获取门神凭证
        if not GlobalOptions.disable_giano:
            cred = giano.get_cred()
            if cred:
                return
        # 如果没有获取到门神凭证，则获取本地配置
        try:
            from common.conf import Config
            conf = Config()
            GlobalOptions.user = conf.get_uname()
            GlobalOptions.token = conf.get_token()
        except Exception as e:
            console.Log.debug(e)
