# -*- coding:utf-8 -*-
""" bundle pull executor"""
import os

from buildin.cli import archive, local_tools
from buildin.cli.artifact import Artifact
from buildin.cli.console import Log
from buildin.cli.exception import IRepoException
from buildin.executors import abstract


class BundlePullArgsProcessor(abstract.RootArgsProcessor):
    """ BundlePull Args Processor"""

    def _process(self):
        if 'repo_type' not in self.options or \
                'module' not in self.options or \
                'resource' not in self.options or \
                'resource_type' not in self.options:
            raise IRepoException('name option is invalid, name pattern must be module[:tag][@rev]')

        # 制品目标位置
        self.options['out_file_name'] = ''
        # default:
        if not self.options['compress'] and not self.options['out']:
            self.options['out_dir'] = self._get_default_out_name()[:-7]
        # if only -c
        elif self.options['compress'] and not self.options['out']:
            self.options['out_dir'] = './'
            self.options['out_file_name'] = self._get_default_out_name()
        # if only --out dirpath/
        elif local_tools.is_dir_path(self.options['out']) and not self.options['compress']:
            self.options['out_dir'] = self.options['out']
        # if only --out filepath
        elif local_tools.is_file_path(self.options['out']) and not self.options['compress']:
            self.options['out_dir'] = self.options['out'] + '/'
        # if --out dirpath/ -c
        elif local_tools.is_dir_path(self.options['out']) and self.options['compress']:
            self.options['out_dir'] = self.options['out']
            self.options['out_file_name'] = self._get_default_out_name()
        # if --out filepath -c
        elif local_tools.is_file_path(self.options['out']) and self.options['compress']:
            # --out dir/filepath
            if os.path.dirname(self.options['out']):
                self.options['out_dir'] = os.path.dirname(self.options['out']) + '/'
            else:
                self.options['out_dir'] = './'
            if str(self.options['out']).endswith('.tar') or \
                    str(self.options['out']).endswith('.tar.gz'):
                self.options['out_file_name'] = os.path.basename(self.options['out'])
            else:
                self.options['out_file_name'] = os.path.basename(self.options['out']) + '.tar.gz'

        Log.debug('bundle pull out_dir: %s' % self.options['out_dir'])
        Log.debug('bundle pull out_file_name: %s' % self.options['out_file_name'])

        # meta信息的目标位置
        if self.options['with_meta']:
            meta_file_suffix = 'debug' if self.options['resource_type'] == 'nodes' else 'release'
            # 举例：baidu_irepo-test_bundle_26dad05.bundle-debug.yml
            self.options['out_meta_filename'] = \
                self._get_default_out_name()[:-14] + '.bundle-' + meta_file_suffix + '.yml'

            if self.options['compress']:
                self.options['out_meta_dir'] = self.options['out_dir']
            else:
                self.options['out_meta_dir'] = \
                    os.path.dirname(os.path.normpath(self.options['out_dir']))

            Log.debug('bundle pull out_meta_dir: %s' % self.options['out_meta_dir'])
            Log.debug('bundle pull out_meta_filename: %s' % self.options['out_meta_filename'])

        self.options['tmp_out_dir'] = local_tools.generate_artifact_unique_temp_name(
            self.options['repo_type'],
            self.options['module'],
            self.options['resource'],
            'bundle_out')

    def _get_default_out_name(self):
        # e.g.: baidu_nlp_ecnet_1-0-0-1_bundle.tar.gz
        default_name_format = '%(repo_name)s_%(version)s_%(repo_type)s.tar.gz'
        version = self.options['resource']
        if self.options['resource_type'] == 'nodes':
            version = self.options['resource'][:7]
        elif self.options['resource_type'] == 'releases':
            version = self.options['resource'].replace('.', '-')
        return default_name_format % {
            'repo_name': self.options['module'].replace('/', '_'),
            'version': version,
            'repo_type': self.options['repo_type']
        }


class BundlePullExecutor(abstract.IRepoExecutor):
    """bundle pull executor"""

    def __init__(self, executor_class_factory=None):
        """ init """
        super(BundlePullExecutor, self).__init__(BundlePullArgsProcessor,
                                                 executor_class_factory)

    def _do(self):
        local_tools.init_temp_workspace()
        Log.info('start to pull bundle %s' % self.argv['name'])
        local_tools.create_path_dir(os.path.normpath(self.argv['out_dir']))
        # download bundle.yml
        bundle_arti = Artifact.create_artifact_by_resource(self.argv['repo_type'],
                                                           self.argv['module'],
                                                           self.argv['resource_type'],
                                                           self.argv['resource'])
        bundle_arti.do_cache()
        bundle_executorClz = self.executor_class_factory.get('BundleBuildExecutor')

        if self.argv['compress']:
            bundle_out = self.argv['tmp_out_dir']
        else:
            bundle_out = self.argv['out_dir']

        options = {
            'repo_type': 'bundle',
            'name': self.argv['module'],
            'resource_type': self.argv['resource_type'],
            'bundle_file': os.path.join(bundle_arti.get_files_dir(), bundle_arti.get_file_name()),
            'with_meta': self.argv['with_meta'],
            'out_meta_dir': self.argv['out_meta_dir'],
            'out_meta_filename': self.argv['out_meta_filename'],
            'dry_run': False,
            'bundle_out': bundle_out,
            'is_static_bundle': True
        }
        bundle_executorClz(self.executor_class_factory).invoke(options)

        if self.argv['compress']:
            self._archive()
        Log.info('pull bundle finish. file at %s' % self.argv['out_dir'])

    def _archive(self):
        if not os.path.isdir(self.argv['tmp_out_dir']):
            return
        Log.info('start to create bundle archive')
        archive_handler = archive.get_archive_handler(self.argv['out_file_name'])
        if archive_handler:
            archive_handler.create(self.argv['tmp_out_dir'], self.argv['out_file_name'],
                                   self.argv['out_dir'])
