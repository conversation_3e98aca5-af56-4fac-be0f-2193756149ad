"""bundle-init executor"""
from buildin.cli import local_tools
from buildin.cli.console import Log
from buildin.executors import abstract

BUNDLE_TEMPLATE = """---
models:
  - repotype: model
    reponame: %s
    version: latest@
    src: 
      - .
    dest: ./
  
prods:
  - repotype: prod
    reponame: %s
    version: current_build@
    src: 
      - ./output/
    dest: ./
"""


class BundleTemplateExecutor(abstract.IRepoExecutor):
    """bundle template executor"""

    def __init__(self, executor_class_factory=None):
        super(BundleTemplateExecutor, self).__init__(abstract.RootArgsProcessor,
                                                     executor_class_factory)

    def _do(self):
        Log.info('start init bundle.yml')
        if self.argv['git_dir']:
            module = local_tools.get_git_repo_name(self.argv['git_dir'])
        else:
            module = self.argv['module']
        bundle = BUNDLE_TEMPLATE % (module, module)
        with open('bundle.yml', 'w') as f:
            f.write(bundle)
        Log.warn('the bundle.yml can not be use directly, need edit it base on your project')
        Log.info('finish init bundle.yml')
