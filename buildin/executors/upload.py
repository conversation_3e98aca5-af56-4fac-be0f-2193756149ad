"""upload executor"""

import json

from buildin.cli import request_tools
from buildin.cli.console import Log
from buildin.cli.exception import IRepoException
from buildin.cli.options import GlobalOptions
from buildin.executors import abstract


class UploadArgsProcessor(abstract.RootArgsProcessor):
    """argument deep processor for upload executor"""

    def _process(self):
        if not self.options['module']:
            raise IRepoException('bad module name, please append --name $MODULE')


class UploadExecutor(abstract.IRepoExecutor):
    """upload executor"""

    def __init__(self, executor_class_factory=None):
        super(UploadExecutor, self).__init__(UploadArgsProcessor, executor_class_factory)

    def _do(self):
        Log.info('start upload.')
        upload_params = {
            'comment': self.argv['comment']
        }
        upload_url = request_tools.build_url(self.argv['repo_type'], self.argv['module'],
                                             'nodes')
        response = request_tools.do_upload_file(url=upload_url,
                                                opened_file=open(self.argv['file_path']),
                                                params=upload_params)
        Log.debug(response.content)
        request_tools.check_response(response)
        revision = json.loads(response.content)['revision']
        if GlobalOptions.quiet:
            print(revision)
        Log.info('artifact revision: %s' % revision)
        Log.info('end upload.')
