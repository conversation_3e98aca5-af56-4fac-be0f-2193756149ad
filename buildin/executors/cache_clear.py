# -* encoding:utf-8 -*-
"""bundle-init executor"""
import datetime
import os
import shutil

from buildin.cli import cachemate
from buildin.cli import local_tools
from buildin.cli.console import Log
from buildin.cli.options import KeySafeDict
from functools import cmp_to_key
from . import abstract


class CacheClearExecutor(abstract.IRepoExecutor):
    """ cache clear executor """

    def __init__(self, executor_class_factory=None):
        super(CacheClearExecutor, self).__init__(abstract.RootArgsProcessor,
                                                 executor_class_factory)

    def _do(self):
        if not os.path.exists(cachemate.CacheMeta.CACHE_HOME):
            Log.info('has no cache in %s' % cachemate.CacheMeta.CACHE_HOME)
            return
        if self.argv['reserve_number'] == 0 and self.argv['reserve_day'] == 0:
            self._clean_all()
        else:
            self._do_clean_cache()
        Log.warn('you better make sure that there is no running bundle task.')
        Log.info('cache clear finished')

    def _clean_all(self):
        Log.info('clear cache %s' % cachemate.CacheMeta.CACHE_HOME)
        shutil.rmtree(cachemate.CacheMeta.CACHE_HOME)

    def _do_clean_cache(self):
        Log.info('start to clear cache at %s' % cachemate.CacheMeta.CACHE_HOME)
        types = local_tools.get_sub_dirs(cachemate.CacheMeta.CACHE_HOME)
        for type_full_dir in types:
            if os.path.basename(type_full_dir) in ['model', 'prod', 'bundle']:
                self._clean_artifact(type_full_dir)
            else:
                Log.warn('%s lack of standardization, will be deleted' % type_full_dir)
                local_tools.remove(type_full_dir)

    def _clean_artifact(self, type_full_dir):
        clean_method_map = KeySafeDict({'nodes': self._clean_nodes,
                                        'releases': self._clean_releases})
        repo_dirs = local_tools.get_sub_dirs(type_full_dir, depth=3)
        for repo_dir in repo_dirs:
            resource_types = local_tools.get_sub_dirs(repo_dir)
            for resource_type_dir in resource_types:
                resource_type = os.path.basename(resource_type_dir)
                clean_method = clean_method_map.get(resource_type)
                if clean_method:
                    clean_method(resource_type_dir)
                else:
                    Log.warn('%s lack of standardization, will be deleted' % resource_type_dir)

    def _clean_nodes(self, nodes_dir):
        """ nodes 按照设置的保留版本数量进行清理 """

        def _compare(x, y):
            """ 按照创建时间排序(顺序-从早到晚) """
            stat_x = os.stat(x)
            stat_y = os.stat(y)
            if stat_x.st_ctime < stat_y.st_ctime:
                return -1
            elif stat_x.st_ctime > stat_y.st_ctime:
                return 1
            else:
                return 0

        def _clean_nodes_by_reserve_number():
            if len(revision_dirs) <= self.argv['reserve_number']:
                return

            for index in range(len(revision_dirs) - self.argv['reserve_number']):
                local_tools.remove(revision_dirs[index])

        def _clean_nodes_by_reserve_day():
            if len(revision_dirs) <= 0:
                return

            for index in range(len(revision_dirs)):
                revision_created_timestamp = os.stat(revision_dirs[index]).st_ctime
                reserve_timestamp = \
                    (datetime.datetime.now() - datetime.timedelta(days=self.argv['reserve_day'])).strftime("%s")
                if int(revision_created_timestamp) < int(reserve_timestamp):
                    local_tools.remove(revision_dirs[index])

        revision_dirs = local_tools.get_sub_dirs(nodes_dir)
        # 删除不规范路径
        for revision_dir in revision_dirs:
            if len(os.path.basename(revision_dir)) != 32:
                Log.warn('%s lack of standardization, will be deleted' % revision_dir)
                local_tools.remove(revision_dir)
                revision_dirs.remove(revision_dir)

        # revision_dirs.sort(_compare)
        revision_dirs.sort(key=cmp_to_key(_compare))
        if self.argv['reserve_number']:
            _clean_nodes_by_reserve_number()
        if self.argv['reserve_day']:
            _clean_nodes_by_reserve_day()

    def _clean_releases(self, releases_dir):
        """ releases 全部清理 不做保留 """
        local_tools.remove(releases_dir)
