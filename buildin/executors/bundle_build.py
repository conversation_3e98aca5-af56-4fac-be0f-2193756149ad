# -*- coding:utf-8 -*-
"""bundle executor"""

import json
import os
import platform

from buildin.cli import request_tools, local_tools
from buildin.cli.artifact import Artifact
from buildin.cli.cachemate import <PERSON>acheMeta
from buildin.cli.console import Log
from buildin.cli.exception import IRepoException
from buildin.cli.decorator import log_time
from buildin.cli.process_bar import SingleProcessBar
from buildin.executors import abstract
from buildin.cli import runtime
from functools import reduce


class BundleBuildArgsProcessor(abstract.RootArgsProcessor):
    """argument deep processor for bundle executor"""

    def _process(self):
        self.options['prod_path'] = self.options.get('prod_path', '')
        self.options['resource_type'] = self.options.get('resource_type', 'nodes')

        if not self.options['out_meta_filename']:
            if self.options['resource_type'] == 'releases':
                self.options['out_meta_filename'] = 'bundle-release.yml'
            elif self.options['resource_type'] == 'nodes':
                self.options['out_meta_filename'] = 'bundle-debug.yml'

        if self.options['out_meta_dir'] is None:
            self.options['out_meta_dir'] = self.options['bundle_out']

        if not self.options.get('module'):
            raise IRepoException('bad module name, please append --name $MODULE')

        # 是否为静态打包，若是，则不获取动态的环境变量
        if self.options['is_static_bundle'] is None:
            self.options['is_static_bundle'] = False


class ArtifactOperation(object):
    """ 制品操作 """

    def __init__(self, artifact):
        self.artifact = artifact
        self.operations = []

    def add_operation(self, srcs, dest):
        """ 添加操作 """
        if isinstance(srcs, list):
            for src in srcs:
                self.operations.append({'src': src, 'dest': dest})
        else:
            self.operations.append({'src': srcs, 'dest': dest})

    def add_base_out(self, base_out):
        """ 为操作中的dest加上base(为了处理bundle) """

        def __add_base(operation):
            operation['dest'] = os.path.join(base_out, operation['dest'])

        list(map(__add_base, self.operations))

    def __repr__(self):
        return 'ArtifactOperation(artifact=%(artifact)r,' \
               'operations=%(operations)r),' % self.__dict__

    def do_copy(self, base_out, process_bar):
        """ 从缓存中拷贝制品 """
        source_dir = self.artifact.get_files_dir()
        for op in self.operations:
            target = os.path.join(base_out, op['dest'])
            local_tools.create_path_dir(target)
            local_tools.do_copy(os.path.join(source_dir, op['src']),
                                target)
            process_bar.set_process()

    def __eq__(self, other):
        if other is None:
            return False
        if not isinstance(other, ArtifactOperation):
            return False
        return self.artifact == other.artifact

    def __hash__(self):
        return hash(self.artifact)


class BundleBuildExecutor(abstract.IRepoExecutor):
    """bundle executor"""

    def __init__(self, executor_class_factory=None):
        super(BundleBuildExecutor, self).__init__(BundleBuildArgsProcessor, executor_class_factory)

    def _do(self):
        Log.info('build bundle start.')
        if not os.path.isfile(self.argv['bundle_file']):
            raise IRepoException('not found bundle file at %s' % self.argv['bundle_file'])
        if os.path.normpath(self.argv['bundle_out']) != ".":
            local_tools.remove(self.argv['bundle_out'])
        local_tools.create_path_dir(self.argv['bundle_out'])

        Log.info("STEP1: build bundle.yml")

        checkpoint = not self.argv['is_static_bundle']
        bundle_info = self.__get_bundle(repo_type=self.argv['repo_type'],
                                        repo_name=self.argv['module'],
                                        bundle_yaml_path=self.argv['bundle_file'],
                                        checkpoint=checkpoint)
        if self.argv['with_meta']:
            bundle_yml = os.path.join(self.argv['out_meta_dir'], self.argv['out_meta_filename'])
            local_tools.create_path_dir(bundle_yml)
            Log.debug('bundle.yml at %s' % bundle_yml)
            with open(bundle_yml, 'w') as f:
                f.write(bundle_info['bundleYaml'])

        if not self.argv['dry_run']:
            self._check_local_dependency()
            self._build_instance(bundle_info['bundle'], self.argv['bundle_out'])
        else:
            Log.info('build bundle finish')

    @log_time
    def _build_instance(self, bundle, bundle_out):
        """ 构建实体包 """
        Log.info("STEP2: analyse bundle.yml")
        artifact_operations = self.__pretreat(bundle)
        Log.debug(artifact_operations)
        Log.info("STEP3: download artifact to cache")
        self._down_artifact(artifact_operations)
        Log.info("STEP4: assemble bundle instance")
        self._assemble_bundle(artifact_operations, bundle_out)
        warn_msg = "local artifact cache at %s, if you not need them in the future, "
        warn_msg += "you can run following command to clear them:\n"
        warn_msg += "       repocli cache clear"
        Log.warn(warn_msg % CacheMeta.CACHE_HOME)
        Log.info("build bundle instance successful, at %s" % self.argv['bundle_out'])

    def _down_artifact(self, artifact_operations):
        shm = '/dev/shm'
        if platform.system() == 'Linux' and not (os.access(shm, os.W_OK) and
                                                 os.access(shm, os.R_OK) and
                                                 os.access(shm, os.X_OK)):
            Log.warn("/dev/shm not mount or current linux login user not has enough permission,"
                     "please visit 'http://wiki.baidu.com/display/PROD4AI/FAQ#FAQ-1' to fix")
            Log.warn("download artifact by single thread, maybe it's slowly")
            self._down_artifact_serially(artifact_operations)
        else:
            try:
                self._down_artifact_multi(artifact_operations)
            except ImportError:
                Log.warn("import multiprocessing lib error, please visit "
                         "'http://wiki.baidu.com/display/PROD4AI/FAQ#FAQ-1' to fix")
                Log.warn("download artifact by single thread, maybe it's slowly")
                self._down_artifact_serially(artifact_operations)

    @log_time
    def _down_artifact_multi(self, artifact_operations):
        from buildin.cli.multi import TrackableProcessListener, TrackableProcess, MultiProcessBar
        multi_process_bar = MultiProcessBar()
        multi_process_bar.begin()
        process_listener = TrackableProcessListener()
        for arti_op in artifact_operations:
            p = TrackableProcess(target=arti_op.artifact.do_cache, args=(multi_process_bar,))
            p.start()
            process_listener.add_monitored_process(p)

        process_listener.start()
        process_listener.join()

        is_succ, error, exception_traceback = process_listener.get_result()
        if not is_succ:
            Log.debug(exception_traceback)
            multi_process_bar.disappear()
            raise error
        multi_process_bar.finish()

    @log_time
    def _down_artifact_serially(self, artifact_operations):
        for arti_op in artifact_operations:
            single_process_bar = SingleProcessBar()
            arti_op.artifact.do_cache(single_process_bar)
            single_process_bar.finish()

    @log_time
    def _assemble_bundle(self, artifact_operations, bundle_out):
        copy_steps = reduce(lambda accumulator, op: accumulator + len(op.operations),
                            artifact_operations, 0)
        process = SingleProcessBar(max_steps=copy_steps)
        try:
            for arti_op in artifact_operations:
                arti_op.do_copy(bundle_out, process)
        except Exception as e:
            process.disappear()
            raise e
        process.finish()

    @log_time
    def __pretreat(self, bundle):
        """ 预处理数据 """
        artifact_operations = []
        for _, artifact_values in list(bundle.items()):
            if not artifact_values:
                continue
            for origin_art in artifact_values:
                artifact = Artifact.create_artifact(repo_type=origin_art['repotype'],
                                                    repo_name=origin_art['reponame'],
                                                    version=origin_art['version'])
                op = ArtifactOperation(artifact)
                # 如果artifact是bundle
                if artifact.repo_type == 'bundle':
                    artifact.do_cache()
                    bundle_yaml_path = os.path.join(artifact.get_files_dir(),
                                                    artifact.get_file_name())
                    bundle_info = self.__get_bundle(repo_type=artifact.repo_type,
                                                    repo_name=artifact.repo_name,
                                                    bundle_yaml_path=bundle_yaml_path,
                                                    checkpoint=False)
                    bundle_artifact_operations = self.__pretreat(bundle_info['bundle'])
                    bundle_dest = origin_art['dest']
                    self.__merge_artifact_operations(artifact_operations,
                                                     bundle_artifact_operations, bundle_dest)
                # 如果artifact已经放入集合
                elif op in artifact_operations:
                    index = artifact_operations.index(op)
                    artifact_operations[index].add_operation(origin_art['src'],
                                                             origin_art['dest'])
                # 如果artifact未放入集合
                else:
                    op.add_operation(origin_art['src'], origin_art['dest'])
                    artifact_operations.append(op)
        return artifact_operations

    def __merge_artifact_operations(self, artifact_operations, child_bundle_artifact_operations,
                                    bundle_dest):
        for child_arti_op in child_bundle_artifact_operations:
            if child_arti_op not in artifact_operations:
                child_arti_op.add_base_out(bundle_dest)
                artifact_operations.append(child_arti_op)
            else:
                index = artifact_operations.index(child_arti_op)
                for op in child_arti_op.operations:
                    artifact_operations[index].add_operation(op['src'],
                                                             os.path.join(bundle_dest, op['dest']))

    def __get_bundle(self, repo_name, repo_type, bundle_yaml_path, checkpoint):
        bundle_build_url = request_tools.build_url(repo_type, repo_name, 'bundles', 'build')
        response = request_tools.do_post(url=bundle_build_url,
                                         body=self.__assemble_request_body(bundle_yaml_path,
                                                                           checkpoint))
        request_tools.check_response(response, repo_type, repo_name)
        return json.loads(response.content)

    def __assemble_request_body(self, bundle_yml_path, checkpoint):
        if not os.path.isfile(bundle_yml_path):
            raise IRepoException('not found bundle.yml at %s' % bundle_yml_path)
        with open(bundle_yml_path, 'r') as f:
            bundle_ctx = f.read()
        if checkpoint:
            # 从agile的环境变量中获取
            check_point = runtime.get_runtime_inject_artifact_from_agile()
            if not check_point.is_empty():
                Log.info("=====================artifact in env start=================")
                Log.info(json.dumps(check_point.__dict__, indent=4))
                Log.info("=====================artifact in env end===================")
            return {'bundleYaml': bundle_ctx, 'bundle': check_point.__dict__}
        else:
            return {'bundleYaml': bundle_ctx}

    def _check_local_dependency(self):
        if not local_tools.command_exist('md5sum'):
            msg = 'not found "md5sum" command, you can install for mac as follow: ' \
                  'brew install md5sha1sum'
            raise IRepoException(msg, IRepoException.MD5SUM_NOT_FOUND)
