""" framework """
import argparse


class CommandProvider(object):
    """ Command Provider"""

    def __init__(self):
        self.sub_command_providers = []
        self.command_name = None
        self.help = ""
        self.arg_adder = ArgAdder()
        self.executor = None


class ArgAdder(object):
    """ argumnet adder"""

    def assemble(self, parser):
        """ parser argument assemble"""
        if len(self.__class__.mro()) > 2:
            self.__class__.mro()[1]().assemble(parser)
        self.add_arg(parser)

    def add_arg(self, parser):
        """ add argument """
        pass


class RootArgAdder(ArgAdder):
    """ global arguments adder"""

    def add_arg(self, parser):
        global_group = parser.add_argument_group("global arguments")
        global_group.add_argument(
            "--timeout", action="store", dest="timeout", metavar='TIME_OUT_SECONDS', default='3600',
            type=int, help="operation max execute time, fail if timeout. default: 3600s(1h)"
        )
        global_group.add_argument(
            "--debug", action="store_true", dest="debug", help=argparse.SUPPRESS
        )
        global_group.add_argument(
            "--quiet", "-q", action="store_true", dest="quiet", default=False,
            help="silent run if not occur error"
        )
        global_group.add_argument(
            "--no-color", action="store_true", dest="no_color", default=False,
            help="console output does not display color"
        )


class AuthArgAdder(RootArgAdder):
    """ common auth argument adder"""

    def add_arg(self, parser):
        common_group = parser.add_argument_group("common auth arguments")
        common_group.add_argument(
            "--name", action="store", dest="name", metavar='REPO_NAME', default='',
            help="e.g.:baidu/product/component[:tag][@rev]"
        )
        common_group.add_argument(
            "--token", "-t", action="store", dest="token", metavar='AUTH_TOKEN', default='',
            help="security auth token."
        )
        common_group.add_argument(
            "--user", "-u", action="store", dest="user", metavar='AUTH_USER', default='',
            help="user name, can be platform user or person user."
        )
        common_group.add_argument(
            "--disable-giano", action="store_true", dest="disable_giano",
            help="disable giano and use user & token. otherwise repocli use giano to "
                 "authenticate with relay-login user default"
        )
