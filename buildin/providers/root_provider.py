""" root """
import buildin.providers.abstract as abstract
import buildin.providers.bundle_provider as bundle_provider
from buildin.providers import cache_provider


class Root<PERSON><PERSON>ider(abstract.CommandProvider):
    """ bundle command provider """

    def __init__(self):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.command_name = "repocli"
        self.help = ""
        self.arg_adder = abstract.ArgAdder()
        self.sub_command_providers.append(bundle_provider.BundleProvider())
        self.sub_command_providers.append(cache_provider.CacheProvider())
