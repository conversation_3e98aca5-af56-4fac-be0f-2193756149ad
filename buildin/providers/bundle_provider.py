""" bundle """
from buildin.providers import abstract
from buildin.providers import bundle_template_provider
from buildin.providers import bundle_build_provider
from buildin.providers import bundle_pull_provider
from buildin.providers import bundle_push_provider


class BundleProvider(abstract.CommandProvider):
    """ bundle command provider """

    def __init__(self):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.command_name = "bundle"
        self.help = "bundle command"
        self.arg_adder = BundleArgAdder()
        self.sub_command_providers.append(bundle_template_provider.BundleTemplateProvider())
        self.sub_command_providers.append(bundle_build_provider.BundleBuildProvider())
        self.sub_command_providers.append(bundle_pull_provider.BundlePullProvider())
        self.sub_command_providers.append(bundle_push_provider.BundlePushProvider())


class BundleArgAdder(abstract.ArgAdder):
    """ bundle default argument """

    def add_arg(self, parser):
        """ add argument """
        parser.set_defaults(repo_type='bundle')
