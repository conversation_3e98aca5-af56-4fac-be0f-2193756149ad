""" bundle template """
import buildin.providers.abstract as abstract
from buildin.executors.bundle_template import BundleTemplateExecutor


class BundleTemplateProvider(abstract.CommandProvider):
    """ bundle template command provider """

    def __init__(self):
        super(BundleTemplateProvider, self).__init__()
        self.arg_adder = BundleTemplateArgAdder()
        self.command_name = "template"
        self.help = "init a bundle.yml template"
        self.executor = BundleTemplateExecutor


class BundleTemplateArgAdder(abstract.RootArgAdder):
    """ bundle template argument adder """

    def add_arg(self, parser):
        """ add argument """
        parser.add_argument(
            "--git-dir", action="store", dest="git_dir", metavar='GIT_SRC_DIR',
            default='./', help="the path of git src dir"
        )
