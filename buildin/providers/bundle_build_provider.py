""" bundle build """

import buildin.providers.abstract as abstract
from buildin.executors.bundle_build import BundleBuildExecutor


class BundleBuildProvider(abstract.CommandProvider):
    """ bundle build command provider """

    def __init__(self):
        super(BundleBuildProvider, self).__init__()
        self.command_name = 'build'
        self.help = 'build a bundle instance by bundle.yml'
        self.arg_adder = BundleBuildArgAdder()
        self.executor = BundleBuildExecutor


class BundleBuildArgAdder(abstract.AuthArgAdder):
    """ bundle build argument adder """

    def add_arg(self, parser):
        """ add argument """
        parser.add_argument(
            '--file', '-f', action='store', dest='bundle_file', metavar='BUNDLE_FILE',
            default='bundle.yml',
            help='bundle file of pre download.'
        )
        parser.add_argument(
            '--out', '-o', action='store', dest='bundle_out',
            metavar='BUNDLE_OUT', default='bundle/',
            help='path name (with file name) to store.'
        )
        parser.add_argument(
            '--prod-path', action='store', dest='prod_path', metavar='PROD_PATH', default='',
            help='the output path of the prod repo which name as same as the bundle repo.'
        )
        parser.add_argument(
            '--with-meta', action='store_true', dest='with_meta', default=True,
            help='download metadata at the same time.'
        )
        parser.add_argument(
            '--dry-run', action='store_true', dest='dry_run', default=False,
            help='only to check bundle, rather than build a bundle instance'
        )
