""" cache """
from buildin.providers import abstract
from buildin.providers import cache_clear_provider


class CacheProvider(abstract.CommandProvider):
    """ cache command provider """

    def __init__(self):
        super(CacheProvider, self).__init__()
        self.command_name = "cache"
        self.help = "cache operation"
        self.arg_adder = abstract.RootArgAdder()
        self.sub_command_providers.append(cache_clear_provider.CacheClearProvider())
