""" bundle pull """
import buildin.providers.abstract as abstract
from buildin.executors.upload import UploadExecutor


class BundlePushProvider(abstract.CommandProvider):
    """ bundle push command provider """

    def __init__(self):
        super(BundlePushProvider, self).__init__()
        self.command_name = "push"
        self.help = "push bundle"
        self.arg_adder = BundlePushArgAdder()
        self.executor = UploadExecutor


class BundlePushArgAdder(abstract.AuthArgAdder):
    """ bundle push argument adder """

    def add_arg(self, parser):
        """ add argument """
        parser.add_argument(
            "--file", "-f", action="store", dest="file_path", metavar='BUNDLE_FILE',
            default='./bundle.yml',
            help="bundle file after build"
        )
        parser.add_argument(
            "--comment", action="store", dest="comment", metavar='COMMENT', required=True,
            help='comment'
        )
