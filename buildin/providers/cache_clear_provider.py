# -* encoding:utf-8 -*-
""" 清理缓存子命令 """
from buildin.executors.cache_clear import CacheClearExecutor
from buildin.providers import abstract


class CacheClearProvider(abstract.CommandProvider):
    """ CacheClearProvider """

    def __init__(self):
        super(CacheClearProvider, self).__init__()
        self.command_name = "clear"
        self.help = "clear cache"
        self.arg_adder = CacheClearArgAdder()
        self.executor = CacheClearExecutor


class CacheClearArgAdder(abstract.RootArgAdder):
    """ CacheClearArgAdder """

    def add_arg(self, parser):
        """ add argument """
        '''
        清理方案：
            1. 仅指定参数reserve=n，按照制品保留数量清理，每个node最多保存n个制品
            2. 仅指定参数day=n，按照制品保留时间清理，每个node只保存创建时间 > (当前时刻-n天)内的制品
            3. 同时指定参数reserve=m, day=n, 先按照制品保留数量清理，再按照制品保留时间清理 
            4. 若不指定参数，则全部清理
        '''
        parser.add_argument(
            "--reserve", action="store", type=int, dest="reserve_number", metavar='INTEGER',
            default='0', help="number of revision retained per artifact by cache created time"
        )

        parser.add_argument(
            "--day", action="store", type=int, dest="reserve_day", metavar='INTEGER',
            default='0', help="retained per artifact with creation time less than the specified number of days"
        )