""" bundle pull """
import buildin.providers.abstract as abstract
from buildin.executors.bundle_pull import BundlePullExecutor


class BundlePullProvider(abstract.CommandProvider):
    """ bundle pull command provider """

    def __init__(self):
        super(BundlePullProvider, self).__init__()
        self.command_name = "pull"
        self.help = "pull bundle"
        self.arg_adder = BundlePullArgAdder()
        self.executor = BundlePullExecutor


class BundlePullArgAdder(abstract.AuthArgAdder):
    """ bundle pull argument adder """

    def add_arg(self, parser):
        """ add argument """
        parser.add_argument(
            "--mode", action="store", dest="mode", metavar='MODE', default='P2P',
            help="download mode, <P2P> or <HTTPS>, default <P2P>"
        )
        parser.add_argument(
            "--out", "-o", action="store", dest="out", metavar='pathName', default='',
            help="path name (with file name) to store."
        )
        parser.add_argument(
            "--compress", "-c", action="store_true", dest="compress", default=False,
            help="build and pack to a bundle tar."
        )
        parser.add_argument(
            "--limit", action="store", dest="limit", metavar='UP/DOWN_LIMIT', default='50',
            help="down speed limit. default: 50M/S"
        )
        parser.add_argument(
            '--with-meta', action='store_true', dest='with_meta', default=False,
            help='download metadata at the same time.'
        )
