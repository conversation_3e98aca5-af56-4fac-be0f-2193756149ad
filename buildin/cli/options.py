# -* encoding:utf-8 -*-quit
""" options for client"""


class GlobalOptions(object):
    """ global options """
    user = ''
    token = ''
    debug = False
    quiet = False
    no_color = False
    disable_giano = False
    timeout_seconds = 600
    token_in_env = ''

    @staticmethod
    def get_timeout_seconds():
        """ 获取超时时间 """
        return GlobalOptions.timeout_seconds


class KeySafeDict(dict):
    """ key安全的字典，无key的value会返回None """

    def __getitem__(self, item):
        if item in self:
            return super(KeySafeDict, self).__getitem__(item)
        else:
            return None
