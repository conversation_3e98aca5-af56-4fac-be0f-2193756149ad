# -* encoding:utf-8 -*-
""" 异常 """


class IRepoException(Exception):
    """ exception raise by client itself"""
    BAD_ARGS = 1
    SEED_FAILED = 2
    GKO3_FAILED = 3
    CHECK_FAILED = 4
    MV_FAILED = 5
    BAD_REQUEST = 6
    TIME_OUT = 7
    BAD_LOCAL_CMD = 8
    USER_CANCEL = 9
    REQUEST_TIMEOUT = 10
    SERVER_FORBIDDEN = 11
    NO_FREE_SPACE = 12
    ARTIFACT_BEEN_ROLLBACK = 13
    MD5SUM_NOT_FOUND = 14
    UNKNOWN = 99

    def __init__(self, message, code=BAD_ARGS):
        super(IRepoException, self).__init__(message)
        self.code = code
        self.message = message


class IRepoTimeoutException(BaseException):
    """ timeout exception for operation"""
    pass
