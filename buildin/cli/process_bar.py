# -*- coding: UTF-8 -*-
""" 进度条 """
from buildin.cli.console import ConsoleTools


class ProcessBar(object):
    """ 进度条基类 """

    def set_process(self, percentage=1):
        """ 设置进度 """
        pass

    def set_title(self, title):
        """ 设置标题 """
        pass

    def finish(self):
        """ 使进度条结束 """
        pass

    def disappear(self):
        """ 使进度条消失 """
        pass


NONE_PROCESS_BAR = ProcessBar()

# 防止终端尺寸太小，导致进度条换行
BAR_MAX_LENGTH = 40


def get_bar_text(title, finished_steps, total_steps, done_info='Done'):
    """
    效果为 title [>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>]100.00% Done
    :param title: 标题
    :param finished_steps: 已完成步骤
    :param total_steps: 总步骤
    :param done_info: 结束字符
    :return:
    """
    finished_show_length = BAR_MAX_LENGTH * (finished_steps * 1.0 / total_steps)
    remained_show_length = BAR_MAX_LENGTH - finished_show_length
    percent = finished_steps * 100 // total_steps
    text = ''.join([title, ' [', '>' * int(finished_show_length), '-' * int(remained_show_length),
                    ']', '%.2f' % percent, '%'])
    if percent >= 100:
        text = text + ' ' + done_info
    text += '\r'
    return text


class SingleProcessBar(ProcessBar):
    """
    单线程显示处理进度的类
    调用该类相关函数即可实现处理进度的显示
    """

    def __init__(self, max_steps=100, title='', done_info='Done'):
        """
        :param title:      标题
        :param max_steps:  需要处理的次数
        :param done_info:  结束时打印的字体
        """
        self.max_steps = int(max_steps)
        self.title = title
        self.percentage = 0
        self.done_info = done_info

    def set_process(self, percentage=None):
        """ 设置进度 """
        if percentage is not None:
            self.percentage = percentage
        else:
            self.percentage += 1
        ConsoleTools.clear_line()
        if self.percentage == 0:
            ConsoleTools.stderr_flush(self.title + ' ...' + '\r')
            return
        if self.percentage > self.max_steps:
            self.percentage = self.max_steps
        process_text = get_bar_text(self.title, self.percentage, self.max_steps, self.done_info)
        ConsoleTools.stderr_flush(process_text)

    def finish(self):
        """ 使进度条结束 """
        if self.percentage == 0:
            return
        print()

    def disappear(self):
        """ 使进度条消失 """
        ConsoleTools.clear_line()

    def set_title(self, title):
        self.title = title