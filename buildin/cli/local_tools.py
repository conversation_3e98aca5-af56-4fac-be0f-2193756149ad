# -* encoding:utf-8 -*-
""" 本地操作系统工具 """
import subprocess
import os
import re
import shutil
import uuid

from buildin.cli.console import Log
from buildin.cli.exception import IRepoException

TEMP_WORKSPACE_ROOT = '.artifact_workspace'

ICODE_URI_PATTERN = r'[^\s]+\s+(?:ssh|https):\/\/([^@]+)@icode(?:test|qa)?' \
                    r'\.baidu\.com.*((?:\/[^\/]+){3})\s\([^\)]+\)'


def remove(path):
    """ 删除文件/文件夹 """
    if not os.path.exists(path):
        Log.debug('%s not found' % path)
    if os.path.isfile(path):
        os.remove(path)
    elif os.path.isdir(path):
        shutil.rmtree(path)
    Log.debug("%s has been removed" % path)


def get_sub_dirs(root, depth=1):
    """ 获取深度为depth的子目录路径集合 """
    if not os.path.exists(root):
        return []
    root_length = len(root.split(os.path.sep))
    result_path = []

    def _recursion_list(root_path, current_depth):
        paths = os.listdir(root_path)
        for path in paths:
            full_path = os.path.join(root_path, path)
            path_length = len(full_path.split(os.path.sep))
            if path_length - root_length == depth:
                result_path.append(full_path)
            elif path_length - root_length > depth:
                return
            else:
                if os.path.isdir(full_path):
                    _recursion_list(full_path, current_depth + 1)

    _recursion_list(root, 0)
    return result_path


def do_copy(src, dest):
    """
    src=file && dest=file :rename
    src=dir  && dest=dir  :move all in src to dest(not contains dir itself)
    src=file && dest=dir  :move file to dest
    src=dir  && dest=file :think dest file path as dir path
    """
    if os.path.isdir(src):
        src = os.path.normpath(src) + '/.'
    create_path_dir(dest)
    exe_cmd("cp -af '%s' '%s'" % (src, os.path.normpath(dest)))


def create_path_dir(path):
    """
    create dir of path if not exist
    如果是文件路径，则创建其父路径
    如果是目录路径(以/结尾)，则创建此目录
    """
    path_dir = os.path.dirname(path)
    if path and path != '' and path_dir != '' and not os.path.exists(path_dir):
        Log.debug('make dir for %s ' % path)
        os.makedirs(path_dir)


def exe_cmd(cmd_ctx):
    """ exe local command """
    Log.debug('RUN [%s]' % cmd_ctx)
    # st, out = commands.getstatusoutput(cmd_ctx)
    result = subprocess.getstatusoutput(cmd_ctx)
    st = result[0]  # 获取状态码
    out = result[1]  # 获取输出结果
    if st != 0:
        raise IRepoException("RUN [%s] failed!\n%s" % (cmd_ctx, out), IRepoException.BAD_LOCAL_CMD)
    return out


def command_exist(command):
    """
    判断本地命令是否存在
    如果是命名名字，则在PATH环境变量中查找
    如果是绝对路径，则判断是否存在此命令文件
    """
    if os.path.isabs(command):
        return os.path.isfile(command)
    for cmd_path in os.environ['PATH'].split(':'):
        if os.path.isdir(cmd_path) and command in os.listdir(cmd_path):
            return True
    return False


def is_dir_path(path):
    """ path is a dir path or not """
    if path is None:
        return False
    return path.endswith('/')


def is_file_path(path):
    """ path is a file path or not """
    if path is None:
        return False
    return not is_dir_path(path)


def init_temp_workspace():
    """ init temp workspace """
    if not os.path.exists(TEMP_WORKSPACE_ROOT):
        exe_cmd("mkdir -p %s" % TEMP_WORKSPACE_ROOT)


def clean_tmp_workspace():
    """ clean tmp workspace """
    if os.path.exists(TEMP_WORKSPACE_ROOT):
        exe_cmd("rm -rf %s" % TEMP_WORKSPACE_ROOT)


def generate_artifact_unique_temp_name(repo_type='', repo_name='', resource='', suffix='temp'):
    """ generate a artifact unique temp name in workspace """
    random_uuid = str(uuid.uuid1())
    return os.path.join(TEMP_WORKSPACE_ROOT, "_".join([repo_type, repo_name.replace('/', '-'),
                                                       resource,
                                                       random_uuid, suffix]))


def get_git_repo_name(git_dir='./'):
    """ get git repo name from git directory """
    if not os.path.isdir(os.path.join(git_dir, '.git')):
        return ''
    for gr in exe_cmd('cd %s && git remote -v' % git_dir).split('\n'):
        gr_matcher = re.search(re.compile(ICODE_URI_PATTERN), gr)
        if gr_matcher is not None:
            return gr_matcher.group(2)[1:]
    return ''
