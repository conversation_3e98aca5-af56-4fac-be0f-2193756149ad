# -* encoding:utf-8 -*-
""" archive service """
import os
import mimetypes
import subprocess

from requests_toolbelt.downloadutils import tee

from buildin.cli import local_tools
from buildin.cli.exception import IRepoException
from buildin.cli.console import Log
from buildin.cli.process_bar import NONE_PROCESS_BAR

NONE_ARCHIVE = 'NONE'


class Archive(object):
    """ 归档操作基类 """

    mime_type = ''
    suffix = ''
    supported_streaming_extract = False

    @classmethod
    def create(cls, src_path, target_file_name, target_path):
        """
        create
        :param src_path: where to create a archive
        :param target_file_name: file name after create
        :param target_path: which dir archive out to move
        :return:
        """
        # add default suffix for target_file_name
        if not target_file_name.endswith(cls.suffix):
            target_file_name = target_file_name + cls.suffix
        if not local_tools.is_dir_path(target_path):
            target_path = target_path + '/'
        if not target_path:
            target_path = './'
        local_tools.create_path_dir(target_path)
        cls._do_create(src_path, target_file_name, target_path)
        local_tools.remove(src_path)

    @classmethod
    def _do_create(cls, src_path, target_file_name, target_path):
        pass

    @classmethod
    def extract(cls, src_path):
        """
        extract src_path to parent dir
        :param src_path: where to extract
        :return:
        """
        cls.extract_to_target(src_path, os.path.dirname(src_path))

    @classmethod
    def extract_to_target(cls, src_path, target_path):
        """
        extract src_path to target_path
        """
        pass

    @classmethod
    def extract_remote_to_target(cls, response, target_path,
                                 process_bar=NONE_PROCESS_BAR):
        """
        extract data from remote to target_path
        """
        p = subprocess.Popen(cls._get_extract_remote_cmd() % target_path,
                             shell=True,
                             stdin=subprocess.PIPE,
                             stderr=subprocess.PIPE)
        content_length = int(response.headers['Content-Length'])
        with response:
            with p.stdin as si:
                bytes_saved = 0
                for chunk in tee.tee(response=response, fileobject=si, chunksize=1024 * 1024 * 8):
                    bytes_saved += len(chunk)
                    process_bar.set_process(bytes_saved * 100 / content_length)
        if p.wait() != 0:
            raise IRepoException('extract %s failed %s ' % (cls.suffix, p.stderr.read()),
                                 IRepoException.BAD_LOCAL_CMD)

    @classmethod
    def _get_extract_remote_cmd(cls):
        raise IRepoException('%s archive type not support streaming extract' % cls.suffix)


class TargzArchive(Archive):
    """ tar.gz archive class """
    mime_type = ('application/x-tar', 'gzip')
    suffix = '.tar.gz'
    supported_streaming_extract = True

    @classmethod
    def _do_create(cls, src_path, target_file_name, target_path):
        """ .tar.gz create """
        local_tools.exe_cmd(
            "cd '%s' && tar -czf '%s' ./*" % (src_path, target_file_name))
        local_tools.exe_cmd("mv '%s/%s' '%s'" % (src_path, target_file_name, target_path))

    @classmethod
    def extract_to_target(cls, src_path, target_path):
        """ .tar.gz extract_to_target """
        local_tools.create_path_dir(target_path)
        local_tools.exe_cmd("tar -xzf '%s' -C '%s'" % (src_path, target_path))
        os.remove(src_path)

    @classmethod
    def _get_extract_remote_cmd(cls):
        return "tar -C '%s' -xzf -"


class TarArchive(Archive):
    """ tar archive class """
    mime_type = ('application/x-tar', None)
    suffix = '.tar'
    supported_streaming_extract = True

    @classmethod
    def _do_create(cls, src_path, target_file_name, target_path):
        """ .tar create """
        local_tools.exe_cmd(
            "cd '%s' && tar -cf '%s' ./*" % (src_path, target_file_name))
        local_tools.exe_cmd("mv '%s/%s' '%s'" % (src_path, target_file_name, target_path))

    @classmethod
    def extract_to_target(cls, src_path, target_path):
        """ .tar extract_to_target """
        local_tools.create_path_dir(target_path)
        local_tools.exe_cmd("tar -xf '%s' -C '%s'" % (src_path, target_path))
        os.remove(src_path)

    @classmethod
    def _get_extract_remote_cmd(cls):
        return "tar -C '%s' -xf -"


class ZipArchive(Archive):
    """ zip archive class """
    mime_type = ('application/zip', None)
    suffix = '.zip'

    @classmethod
    def extract_to_target(cls, src_path, target_path):
        """ .zip extract_to_target """
        local_tools.exe_cmd("unzip -o -d '%s' '%s'" % (target_path, src_path))
        os.remove(src_path)


class TgzArchive(Archive):
    """ tgz archive class """
    mime_type = ('application/x-tar', 'gzip')
    suffix = '.tgz'

    @classmethod
    def create(cls, src_path, target_file_name, target_path):
        """ .tgz create """
        TargzArchive.create(src_path, target_file_name, target_path)

    @classmethod
    def extract_to_target(cls, src_path, target_path):
        """ .tgz extract_to_target """
        TargzArchive.extract_to_target(src_path, target_path)

    @classmethod
    def extract_remote_to_target(cls, data, target_path, process_bar=NONE_PROCESS_BAR):
        """ .tgz extract_remote_to_target """
        TargzArchive.extract_remote_to_target(data, target_path, process_bar)


def get_archive_handler(file_name):
    """ guess a archive handler through file mime type by suffix """
    mime_type = mimetypes.guess_type(file_name)
    Log.debug('%s mime-type is %s' % (file_name, mime_type))
    for archiveClz in Archive.__subclasses__():
        if archiveClz.mime_type == mime_type:
            Log.debug('%s archive handler is %s' % (file_name, archiveClz))
            return archiveClz
    return None
