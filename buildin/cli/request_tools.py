# -* encoding:utf-8 -*-
""" 请求组件 """
import json
import sys
import time
import urllib.request
import urllib.parse
import urllib.error
import requests
from requests import RequestException
from requests_toolbelt.downloadutils import tee

from buildin.cli import giano
from buildin.cli.console import Log
from buildin.cli.exception import IRepoException
from buildin.cli.options import GlobalOptions, KeySafeDict
from buildin.cli.process_bar import NONE_PROCESS_BAR

# protocol_and_host = "http://127.0.0.1:8301"
protocol_and_host = "http://irepotest.baidu-int.com"
rest = "rest"
api_version = "v3"
try_times = 3
retry_time_interval_seconds = 2
connection_timeout_seconds = 10


def build_url(*args):
    """
    获取url
    args[0]:必须为repo_type
    args[1]:必须为repo_name
    """
    assert len(args) >= 2
    assert args[0] in ['bundle', 'model', 'prod']
    base_url = '/'.join([protocol_and_host, 'rest', args[0], 'v3', args[1]])
    extend = '/'.join(args[2:])
    return '/'.join([base_url, extend])


def do_get(url, params=None, stream=False):
    """ get 请求 """
    url = __assemble_url(url, params)
    try_time = 1
    while try_time <= try_times:
        try:
            Log.debug('request %s try %s time' % (url, try_time))
            response = requests.get(url, stream=stream,
                                    headers=_get_auth_header(),
                                    timeout=connection_timeout_seconds, verify=False)
            return response
        except requests.Timeout as e:
            Log.debug(e)
            time.sleep(retry_time_interval_seconds)
            try_time += 1
        except RequestException as re:
            Log.debug(re)
            raise IRepoException("request server failed")
    raise IRepoException('request timeout', IRepoException.REQUEST_TIMEOUT)


def do_post(url, body, params=None, stream=False):
    """ post 请求 """
    url = __assemble_url(url, params)
    try_time = 1
    while try_time <= try_times:
        try:
            Log.debug('request %s try %s time' % (url, try_time))
            response = requests.post(url, stream=stream, json=body,
                                     headers=_get_auth_header(),
                                     timeout=connection_timeout_seconds, verify=False)
            return response
        except requests.Timeout as e:
            Log.debug(e)
            time.sleep(retry_time_interval_seconds)
            try_time += 1
        except RequestException as re:
            Log.debug(re)
            raise IRepoException("request server failed")
    raise IRepoException('request timeout', IRepoException.REQUEST_TIMEOUT)


def do_upload_file(url, opened_file, params=None):
    """ post上传文件 """
    url = __assemble_url(url, params)
    try_time = 1
    while try_time <= try_times:
        try:
            Log.debug('request %s try %s time' % (url, try_time))
            response = requests.post(url, files={'file': opened_file},
                                     headers=_get_auth_header(),
                                     timeout=connection_timeout_seconds, verify=False)
            return response
        except requests.Timeout as e:
            Log.debug(e)
            time.sleep(retry_time_interval_seconds)
            try_time += 1
        except RequestException as re:
            Log.debug(re)
            raise IRepoException("request server failed")
    raise IRepoException('request timeout', IRepoException.REQUEST_TIMEOUT)


def _get_auth_header():
    auth_header = dict()
    if GlobalOptions.token_in_env:
        auth_header['Authorization'] = 'Bearer ' + GlobalOptions.token_in_env
    else:
        auth_header['IREPO-UNAME'] = GlobalOptions.user
        auth_header['IREPO-TOKEN'] = GlobalOptions.token
        if not GlobalOptions.disable_giano:
            auth_header['GIANO-CRED'] = giano.get_cred()
    auth_header['IREPO'] = ' '.join(sys.argv)
    Log.debug("request header: %s" % auth_header)
    return auth_header


def __assemble_url(url, params=None):
    if not params:
        return url
    url = url + '?'
    for key, value in list(params.items()):
        url = url + '%s=%s&' % (key, value)
        url = url[:-1]
    return url


def check_response(response, *args):
    """ 检查响应 """
    args = list(map(lambda a: str(a), args))
    code = response.status_code
    Log.debug(response.status_code)
    if 200 <= code < 300:
        return
    elif 302 == code:
        return
    elif 403 == code:
        raise IRepoException('current user not have [%s] permission' % '-'.join(args),
                             IRepoException.SERVER_FORBIDDEN)
    elif 404 == code:
        raise IRepoException('not found [%s]' % '-'.join(args))
    else:
        try:
            err_body = KeySafeDict(json.loads(response.content))
        except Exception as e:
            Log.debug(str(e))
            Log.debug(response.content)
            raise IRepoException(str(code) + ' unknown error', IRepoException.UNKNOWN)
        raise IRepoException(str(code) + ' ' + err_body['msg'])


def get_remote_file_name_by_response(response):
    """ 获取文件名 """
    content_header = response.headers['Content-Disposition']
    Log.debug(content_header)
    # 防止中文名字
    return urllib.parse.unquote(str(content_header).split("=")[1])


def write_to_file(response, file_path, process_bar=NONE_PROCESS_BAR):
    """ 响应写入文件 """
    content_length = response.headers['Content-Length']
    bytes_saved = 0
    with open(file_path, 'wb') as save_file:
        for chunk in tee.tee(response, save_file, chunksize=1024 * 1024 * 8):
            bytes_saved += len(chunk)
            process_bar.set_process(bytes_saved * 100 // int(content_length))
