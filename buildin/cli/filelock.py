# -* encoding:utf-8 -*-
""" 文件锁 """
import fcntl


class FileLock(object):
    """
    劝告锁、非强制锁
    阻塞式锁
    无超时时间、进程结束(即使意外崩溃)自动释放、
    使用方式见unit-tests/test_flock.py
    """

    def __init__(self, file_name):
        """ 需要锁的文件 """
        self.file_name = file_name
        self.fd = None

    def acquire(self):
        """ 获取锁 """
        f = open(self.file_name, 'w')
        self.f = f
        fcntl.flock(self.f.fileno(), fcntl.LOCK_EX)

    def release(self):
        """ 释放锁 """
        self.f.close()
