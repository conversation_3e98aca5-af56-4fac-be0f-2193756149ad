# -* encoding:utf-8 -*-
""" 制品本地缓存下载 """
import json
import os
import shutil
import traceback
from abc import ABCMeta
from abc import abstractmethod

from buildin.cli import request_tools, archive, options, local_tools
from buildin.cli.console import Log
from buildin.cli.exception import IRepoException
from buildin.cli.cachemate import Cache<PERSON>eta, CacheStatus
from buildin.cli.options import KeySafeDict
from buildin.cli.process_bar import NONE_PROCESS_BAR


class Artifact(object):
    """ 制品抽象基类 """

    __metaclass__ = ABCMeta

    def __init__(self, repo_type, repo_name, resource_type, resource):
        self.repo_type = repo_type
        self.repo_name = repo_name
        self.resource_type = resource_type
        self.resource = resource
        self.meta = CacheMeta(self._get_meta_path())

    def __eq__(self, other):
        if other is None:
            return False
        if not isinstance(other, Artifact):
            return False
        return self.repo_type == other.repo_type and \
               self.repo_name == other.repo_name and \
               self.resource_type == other.resource_type and \
               self.resource == other.resource

    def __hash__(self):
        return hash('-'.join(self.get_attribute_list()))

    def __repr__(self):
        return 'Artifact(repo_type=%(repo_type)r,' \
               'repo_name=%(repo_name)r,' \
               'resource_type=%(resource_type)r,' \
               'resource=%(resource)r)' % self.__dict__

    def readable(self):
        """ 获取制品的可读性文字表示,与--name参数风格一致 """
        join_symbol = {'releases': ':', 'nodes': '@'}.get(self.resource_type, " ")
        return '%s%s%s' % (self.repo_name, join_symbol, self.resource)

    def get_attribute_list(self):
        """ 获取初始化时的参数数组 """
        return [self.repo_type, self.repo_name, self.resource_type, self.resource]

    @abstractmethod
    def do_cache(self, process_bar=NONE_PROCESS_BAR):
        """ 缓存入口 """
        pass

    def get_files_dir(self):
        """ 制品缓存文件的目录 """
        return os.path.join(self.get_cache_root(), 'files')

    def get_file_name(self):
        """ 获取制品文件原名字 """
        return self.meta.get_file_name()

    def get_cache_root(self):
        """ 制品的缓存root目录 """
        return os.path.join(CacheMeta.CACHE_HOME, self.repo_type, self.repo_name,
                            self.resource_type,
                            self.resource)

    def _get_meta_path(self):
        """ 制品的缓存meta信息路径 """
        return os.path.join(self.get_cache_root(), 'META')

    def _get_md5_path(self):
        """ 获取制品的md5文件路径 """
        return os.path.join(self.get_cache_root(), 'md5')

    @staticmethod
    def create_artifact(repo_type, repo_name, version):
        """ 工厂方法,根据version创建artifact """
        if str.endswith(str(version), '@release'):
            return RemoteReleaseArtifact(repo_type, repo_name, 'releases',
                                         version.replace('@release', ''))
        elif len(version) == 32:
            return RemoteBuildArtifact(repo_type, repo_name, 'nodes', version)
        elif version == 'current_build@':
            return LocalArtifact(repo_type, repo_name, 'current_build', 'current_build')
        else:
            raise IRepoException('not supported %s' % version)

    @staticmethod
    def create_artifact_by_resource(repo_type, repo_name, resource_type, resource):
        """ 工厂方法,根据resource_type、resource创建artifact """
        if resource_type == 'nodes':
            if len(resource) != 32:
                raise IRepoException('revision %s is invalid, length should be 32' %
                                     resource)
            return RemoteBuildArtifact(repo_type, repo_name, resource_type, resource)
        elif resource_type == 'releases':
            if len(str(resource).split(".")) != 4:
                raise IRepoException('version %s is invalid, version should like 1.0.0.1' %
                                     resource)
            return RemoteReleaseArtifact(repo_type, repo_name, resource_type, resource)
        raise IRepoException('not supported %s' % resource)


class LocalArtifact(Artifact):
    """ 本地产出制品 """

    def do_cache(self, process_bar=NONE_PROCESS_BAR):
        if not os.path.isdir('output'):
            raise IRepoException('not found output directory, please build first')

    def get_files_dir(self):
        return './'

    def get_cache_root(self):
        return './'

    def get_file_name(self):
        return ''


class RemoteBuildArtifact(Artifact):
    """ build版本制品 """

    def do_cache(self, process_bar=NONE_PROCESS_BAR):
        """ 入口 """
        self.process_bar = process_bar
        self.__init_root_if_necessary()
        if self.meta.get_status() != CacheStatus.FINISHED:
            self._do_cache()
        else:
            Log.info("%s cache has been download" % self.readable())

    def __init_root_if_necessary(self):
        """ 由操作系统保证原子性 """
        if not os.path.exists(self.get_cache_root()):
            local_tools.exe_cmd("mkdir -p '%s'" % self.get_cache_root())

    def _do_cache(self):
        Log.debug("%s cache downloading" % self.readable())
        Log.debug("try to get %s lock" % self.readable())
        with self.meta:
            Log.debug("get %s lock" % self.readable())
            if self.meta.get_status() == CacheStatus.FINISHED:
                Log.debug("%s cache download finished" % self.readable())
            else:
                while self.meta.get_status() != CacheStatus.FINISHED:
                    self.__do_next_step(self.meta.get_status())

    def __do_next_step(self, status):
        step = {
            CacheStatus.NONE: self.__init_step,
            CacheStatus.INITED: self.__down_file_step,
            CacheStatus.FILE_DOWNED: self.__down_md5_step,
            CacheStatus.MD5_DOWNED: self.__check_md5_step,
            CacheStatus.MD5_CHECKED: self.__finally_step,
        }.get(status)
        self.process_bar.set_title(self.resource[:7] + "-" + step.__doc__)
        self.process_bar.set_process(0)
        step()
        self.process_bar.set_process(100)

    def __init_step(self):
        """INIT ROOT"""
        Log.debug('init cache root %s' % self.get_cache_root())
        self.meta.init_meta()

    def __down_file_step(self):
        """DOWN & EXTRACT"""
        # 初始化files缓存目录
        target_path = self.get_files_dir()
        if os.path.exists(target_path):
            shutil.rmtree(target_path)
        os.makedirs(target_path)

        # 正常下载逻辑
        down_url = request_tools.build_url(self.repo_type, self.repo_name, self.resource_type,
                                           self.resource, 'files?rename=false')
        try:
            # 尝试下载并解压
            self.__try_down_and_extract(down_url)
        except Exception as e:
            Log.debug('%s-%s is not archive file, but endswith archive suffix, will not extract' %
                      (self.readable(), self.meta.get_file_name()))
            Log.debug(e)
            if options.GlobalOptions.debug:
                traceback.print_exc()
            # 下载整体文件
            self.__down_single_file(down_url)

        self.meta.set_status(CacheStatus.FILE_DOWNED)

    def __try_down_and_extract(self, down_url):
        response = request_tools.do_get(down_url, stream=True)
        request_tools.check_response(response, *self.get_attribute_list())
        file_name = request_tools.get_remote_file_name_by_response(response)
        self.meta.set_file_name(file_name)
        archive_handler = archive.get_archive_handler(file_name)
        if archive_handler:
            self.meta.set_archive_type(archive_handler.suffix)
            # 是否支持流式解压
            if archive_handler.supported_streaming_extract:
                archive_handler.extract_remote_to_target(response, self.get_files_dir(),
                                                         self.process_bar)
            else:
                self.__local_extract(archive_handler, response)
        else:
            self.meta.set_archive_type(archive.NONE_ARCHIVE)
            request_tools.write_to_file(response, os.path.join(self.get_files_dir(), file_name),
                                        self.process_bar)

    def __local_extract(self, archive_handler, response):
        local_tools.init_temp_workspace()
        temp_path = local_tools.generate_artifact_unique_temp_name(
            repo_type=self.repo_type,
            repo_name=self.repo_name,
            resource=self.resource,
            suffix='archive_temp')
        try:
            request_tools.write_to_file(response, temp_path, self.process_bar)
            archive_handler.extract_to_target(temp_path, self.get_files_dir())
        finally:
            local_tools.remove(temp_path)

    def __down_single_file(self, down_url):
        response = request_tools.do_get(down_url, stream=True)
        request_tools.check_response(response, *self.get_attribute_list())
        file_name = request_tools.get_remote_file_name_by_response(response)
        self.meta.set_archive_type(archive.NONE_ARCHIVE)
        self.meta.set_file_name(file_name)
        request_tools.write_to_file(response, os.path.join(self.get_files_dir(), file_name),
                                    self.process_bar)

    def __down_md5_step(self):
        """DOWN DIGEST"""

        def __single_file():
            file_name = self.meta.get_file_name()
            md5url = request_tools.build_url(self.repo_type, self.repo_name, self.resource_type,
                                             self.resource, 'files/md5')
            response = request_tools.do_get(md5url)
            request_tools.check_response(response, *self.get_attribute_list())
            md5 = response.content
            with open(self._get_md5_path(), 'wb') as md5_file:
                md5_file.write(md5 + b'  ' + file_name.encode() + b'\n')

        def __archive_file():
            entries_url = request_tools.build_url(self.repo_type, self.repo_name,
                                                  self.resource_type,
                                                  self.resource, 'entries')
            response = request_tools.do_get(entries_url)
            request_tools.check_response(response, *self.get_attribute_list())
            entries = json.loads(response.content)
            if len(entries) != 0 and entries[0]['md5']:
                with open(self._get_md5_path(), 'w') as md5_file:
                    for entry in entries:
                        md5_file.write(entry['md5'] + '  ' + entry['path'] + '\n')

        md5_target_path = self._get_md5_path()
        if os.path.exists(md5_target_path):
            os.remove(md5_target_path)
        archive_type = self.meta.get_archive_type()
        if archive_type == archive.NONE_ARCHIVE:
            __single_file()
        else:
            __archive_file()

        self.meta.set_status(CacheStatus.MD5_DOWNED)

    def __check_md5_step(self):
        """CHECK DIGEST"""
        try:
            if os.path.isfile(self._get_md5_path()):
                local_tools.exe_cmd("cd %(file_dirs)s && md5sum -c '%(md5_file)s'" % {
                    'file_dirs': self.get_files_dir(),
                    'md5_file': self._get_md5_path()
                })
        except Exception as e:
            if not options.GlobalOptions.debug:
                self.__do_clean()
            raise e
        self.meta.set_status(CacheStatus.MD5_CHECKED)

    def __finally_step(self):
        """SAVE FINISHED"""
        self.meta.set_status(CacheStatus.FINISHED)

    def __do_clean(self):
        local_tools.remove(self.get_files_dir())
        local_tools.remove(self._get_meta_path())
        local_tools.remove(self._get_md5_path())


class RemoteReleaseArtifact(Artifact):
    """ 发布版本制品 """

    def __init__(self, repo_type, repo_name, resource_type, resource):
        self.repo_type = repo_type
        self.repo_name = repo_name
        self.resource_type = resource_type
        self.resource = resource
        release_info_url = request_tools.build_url(self.repo_type, self.repo_name,
                                                   self.resource_type,
                                                   self.resource)
        response = request_tools.do_get(release_info_url)
        request_tools.check_response(response, *self.get_attribute_list())
        release_info = KeySafeDict(json.loads(response.content))
        revision = release_info['revision']
        is_rollback = release_info['roll_back']
        if is_rollback:
            raise IRepoException('[%s]%s-%s has been rollback' %
                                 (self.repo_type, self.repo_name, self.resource),
                                 IRepoException.ARTIFACT_BEEN_ROLLBACK)
        build_artifact = RemoteBuildArtifact(repo_type=self.repo_type,
                                             repo_name=self.repo_name,
                                             resource_type='nodes',
                                             resource=revision)
        self.build_artifact = build_artifact

    def do_cache(self, process_bar=NONE_PROCESS_BAR):
        if self.build_artifact.meta.get_status() != CacheStatus.FINISHED:
            Log.debug("%s cache downloading" % self.readable())
            self.build_artifact.do_cache(process_bar)
            Log.debug("%s cache download finished" % self.readable())
        else:
            Log.debug("%s cache has been download" % self.readable())

    def get_files_dir(self):
        return self.build_artifact.get_files_dir()

    def get_file_name(self):
        return self.build_artifact.get_file_name()

    def get_cache_root(self):
        return self.build_artifact.get_cache_root()

    def _get_meta_path(self):
        return self.build_artifact._get_meta_path()

    def _get_md5_path(self):
        return self.build_artifact._get_md5_path()
