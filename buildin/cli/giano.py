# -* encoding:utf-8 -*-quit
""" giano """
import os
import sys
import time

from buildin.cli.console import Log

GIANO_CRED_GENERATE_INTERVAL_SECONDS = 15 * 60


class GianoCache(object):
    """ 门神凭证缓存 """
    last_generate_giano_timestamp = 0
    generator = None
    giano_cred = ''


def get_cred():
    """ 获取门神凭证 """
    # 判断是否超时
    if time.time() - GianoCache.last_generate_giano_timestamp > \
            GIANO_CRED_GENERATE_INTERVAL_SECONDS:
        GianoCache.giano_cred = __do_get_cred()
        GianoCache.last_generate_giano_timestamp = time.time()
    return GianoCache.giano_cred


def __do_get_cred():
    """ get giano cred """
    try:
        sys.path.append(os.path.join(sys.path[0], "lib"))
        import baassw
        generator = __do_get_generator()
        if generator:
            cred = baassw.string_p()
            generator.GenerateCredential(cred.cast())
            if cred.value():
                return cred.value()
    except Exception as e:
        Log.debug(str(e))
    return ''


def __do_get_generator():
    """
    get giano cred generator
    baassw.ClientUtility.Login()方法在父进程运行成功之后，在子进程中再次运行会卡住
    """
    if not GianoCache.generator:
        try:
            sys.path.append(os.path.join(sys.path[0], "lib"))
            import baassw
            baassw.BAAS_Init()
            generator = baassw.ClientUtility.Login()
            if not generator:
                Log.warn('giano login fail, retry...')
                time.sleep(3)
                generator = baassw.ClientUtility.Login()
            GianoCache.generator = generator
        except Exception as e:
            Log.debug(str(e))
    return GianoCache.generator
