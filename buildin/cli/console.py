# -* encoding:utf-8 -*-
""" 控制台打印 """
import os
import sys
import logging
import inspect

from buildin.cli.options import GlobalOptions


class LogLevel(object):
    """ log level """
    DEBUG = 1
    INFO = 1 << 1
    WARN = 1 << 2
    ERROR = 1 << 3
    FATAL = 1 << 4


TEXT_COLOR_RED = '\033[31m'
TEXT_COLOR_GREEN = '\033[32m'
TEXT_COLOR_YELLOW = '\033[33m'
TEXT_COLOR_BLUE = '\033[34m'
TEXT_COLOR_PURPLE = '\033[35m'
TEXT_COLOR_WHITE = '\033[37m'
RESET = '\033[0m'


class Log(object):
    """ console log """
    _level = LogLevel.INFO
    logger = None

    @classmethod
    def set_level(cls, level=LogLevel.INFO):
        """ log level """
        Log._level = level

    @classmethod
    def debug(cls, msg):
        """ debug msg"""
        cls.__get_logger().debug(cls.__wrap_msg(msg))

    @classmethod
    def info(cls, msg):
        """ info msg"""
        if LogLevel.INFO >= Log._level and not GlobalOptions.quiet:
            if GlobalOptions.no_color:
                print("[%s] %s" % ("INFO", msg))
            else:
                print("[%s] %s" % (RichText.render_green_text("INFO"), msg))
            sys.stdout.flush()
        cls.__get_logger().info(cls.__wrap_msg(msg))

    @classmethod
    def warn(cls, msg):
        """ warn msg"""
        if LogLevel.WARN >= Log._level:
            if GlobalOptions.no_color:
                sys.stderr.write("[%s] %s\a\n" % ("WARN", msg))
            else:
                sys.stderr.write("[%s] %s\a\n" % (RichText.render_yellow_text("WARN"), msg))
            sys.stderr.flush()
        cls.__get_logger().warning(cls.__wrap_msg(msg))

    @classmethod
    def error(cls, msg):
        """ error msg"""
        if LogLevel.ERROR >= Log._level:
            if GlobalOptions.no_color:
                ConsoleTools.stderr_flush("[%s] %s\a\n" % ("ERROR", msg))
            else:
                ConsoleTools.stderr_flush("[%s] %s\a\n" % (RichText.render_red_text("ERROR"), msg))
        cls.__get_logger().error(cls.__wrap_msg(msg))

    @classmethod
    def fatal(cls, msg):
        """ fatal msg"""
        if LogLevel.FATAL >= Log._level:
            if GlobalOptions.no_color:
                ConsoleTools.stderr_flush("[%s] %s\a\n" % ("FATAL", msg))
            else:
                ConsoleTools.stderr_flush("[%s] %s\a\n" % (RichText.render_red_text("FATAL"), msg))
        cls.__get_logger().fatal(cls.__wrap_msg(msg))

    @classmethod
    def __get_logger(cls):
        """ get logger """
        if not Log.logger:
            Log.logger = cls.__init_logger()
        return Log.logger

    @classmethod
    def __init_logger(cls):
        logging.basicConfig(level=logging.DEBUG,
                            filename=os.path.join(sys.path[0], "cli.log"),
                            filemode='w',  # 仅保留一次执行的日志
                            format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')
        logger = logging.getLogger()
        return logger

    @classmethod
    def __wrap_msg(cls, msg):
        """ 包装message """
        clz_name = inspect.stack()[2][1]
        func_name = inspect.stack()[2][3]
        line_number = inspect.stack()[2][2]
        return "".join([os.path.basename(clz_name), ":",
                        str(line_number), " - ",
                        func_name, " : ", str(msg)])


class RichText(object):
    """
    rich text display
    """

    @classmethod
    def render(cls, text, bg_color, color=TEXT_COLOR_WHITE):
        """ render """
        result = []
        result.append(bg_color)
        result.append(color)
        result.append(text)
        result.append(RESET)
        return ''.join(result)

    @classmethod
    def render_text_color(cls, text, color):
        """render text with color
        """
        return ''.join([color, text, RESET])

    @classmethod
    def render_green_text(cls, text):
        """
        render green text
        """
        return cls.render_text_color(text, TEXT_COLOR_GREEN)

    @classmethod
    def render_red_text(cls, text):
        """
        render red text
        """
        return cls.render_text_color(text, TEXT_COLOR_RED)

    @classmethod
    def render_purple_text(cls, text):
        """
        render purple text
        """
        return cls.render_text_color(text, TEXT_COLOR_PURPLE)

    @classmethod
    def render_blue_text(cls, text):
        """
        render blue text
        """
        return cls.render_text_color(text, TEXT_COLOR_BLUE)

    @classmethod
    def render_yellow_text(cls, text):
        """render yellow text
        """
        return cls.render_text_color(text, TEXT_COLOR_YELLOW)


class ConsoleTools(object):
    """ 控制台打印工具 """

    @staticmethod
    def cursor_up_move(n=1):
        if n == 0:
            return
        sys.stderr.write("\33[%sA" % str(n))
        sys.stderr.flush()

    @staticmethod
    def cursor_down_move(n=1):
        if n == 0:
            return
        sys.stderr.write("\33[%sB" % str(n))
        sys.stderr.flush()

    @staticmethod
    def clear_line():
        sys.stderr.write("\033[K")
        sys.stderr.flush()

    @staticmethod
    def stderr_flush(msg):
        sys.stderr.write(msg)
        sys.stderr.flush()

    @staticmethod
    def stdout_flush(msg):
        sys.stdout.write(msg)
        sys.stdout.flush()
