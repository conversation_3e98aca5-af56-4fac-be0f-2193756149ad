# -* encoding:utf-8 -*-
""" 多进程相关组件 """
import multiprocessing
import threading
import time
import traceback

from buildin.cli.console import ConsoleTools
from buildin.cli.process_bar import get_bar_text, ProcessBar


class TrackableProcess(multiprocessing.Process):
    """
    可追踪异常与退出状态的进程
    """

    def __init__(self, *args, **kwargs):
        multiprocessing.Process.__init__(self, *args, **kwargs)
        self._pconn, self._cconn = multiprocessing.Pipe()
        self._exception = None

    def run(self):
        """ run """
        try:
            multiprocessing.Process.run(self)
            self._cconn.send(None)
        except BaseException as e:
            tb = traceback.format_exc()
            self._cconn.send((e, tb))

    @property
    def exception(self):
        """ 异常状态 """
        if self._pconn.poll():
            self._exception = self._pconn.recv()
        return self._exception


class TrackableProcessListener(threading.Thread):
    """
    TrackableProcess 监听器: 监听子进程是否存在异常，若存在异常，则停止全部子线程，并返回异常情况
    """

    def __init__(self):
        threading.Thread.__init__(self)
        self.monitored_process_list = []
        self.is_success = False
        self.error = None
        self.exception_traceback = None

    def add_monitored_process(self, process):
        """ 添加TrackableProcess """
        assert self.is_alive() == False, "can't add process when the listener has started"
        self.monitored_process_list.append(process)

    def run(self):
        """ run """
        finished = False
        while not finished:
            time.sleep(1)
            succeed_process_number = 0
            for process in self.monitored_process_list:
                if process.is_alive():
                    continue
                if process.exception:
                    error, exception_traceback = process.exception
                    self.is_success = False
                    self.error = error
                    self.exception_traceback = exception_traceback
                    self.__terminate_all_proc()
                    return
                else:
                    succeed_process_number = succeed_process_number + 1
            if succeed_process_number == len(self.monitored_process_list):
                finished = True
                self.is_success = True

    def __terminate_all_proc(self):
        for proc in self.monitored_process_list:
            proc.terminate()

    def get_result(self):
        """ 获取运行结果 """
        self.join()
        return self.is_success, self.error, self.exception_traceback


multiprocessing.set_start_method('fork')
multi_process_bar_manager = multiprocessing.Manager()


class MultiProcessBar(ProcessBar):
    """
    多线程显示进度条的类
    """
    _instance_lock = multiprocessing.Lock()
    manager = multiprocessing.Manager()
    # 当前实例代理
    current_instance_proxy = None

    def __new__(cls, *args, **kwargs):
        if MultiProcessBar.current_instance_proxy:
            return MultiProcessBar.current_instance_proxy
        with MultiProcessBar._instance_lock:
            if MultiProcessBar.current_instance_proxy is None:
                o = object.__new__(cls)
                MultiProcessBar.current_instance_proxy = o
                o.process_bar_map = MultiProcessBar.manager.dict()
                o.is_stop = False
                return MultiProcessBar.current_instance_proxy

    def begin(self):
        """ 开始监听显示输出文本 """
        t = threading.Thread(target=self.__show_out)
        t.setDaemon(True)
        t.start()

    def __show_out(self):
        while not self.is_stop:
            for process_name, bar_info in list(self.process_bar_map.items()):
                ConsoleTools.clear_line()
                ConsoleTools.stderr_flush(self.__get_text(bar_info) + "\n")
            ConsoleTools.cursor_up_move(len(self.process_bar_map))
            time.sleep(0.5)

    def __get_text(self, bar_info):
        if bar_info.percentage == 0:
            return bar_info.title + ' ...'
        # 计算完成进度，格式为xx.xx%
        return get_bar_text(bar_info.title, bar_info.percentage, 100)

    def disappear(self):
        """ 使进度条消失 """
        self.is_stop = True
        for _ in range(len(self.process_bar_map)):
            ConsoleTools.clear_line()
            ConsoleTools.cursor_down_move()
        ConsoleTools.cursor_up_move(len(self.process_bar_map))
        MultiProcessBar.current_instance_proxy = None

    def finish(self):
        """ 使进度条结束 """
        self.is_stop = True
        for process_name, bar_info in list(self.process_bar_map.items()):
            ConsoleTools.clear_line()
            bar_info.percentage = 100
            ConsoleTools.stderr_flush(self.__get_text(bar_info) + "\n")
        ConsoleTools.cursor_down_move()
        MultiProcessBar.current_instance_proxy = None

    def set_title(self, title):
        """ 设置标题 """
        process = multiprocessing.current_process()
        if process.name not in self.process_bar_map:
            self.process_bar_map[process.name] = self.__init_bar_info()
        self.process_bar_map[process.name].title = title

    def set_process(self, percentage=1):
        """ 设置进度 """
        process = multiprocessing.current_process()
        if process.name not in self.process_bar_map:
            self.process_bar_map[process.name] = self.__init_bar_info()
        self.process_bar_map[process.name].percentage = percentage

    def __init_bar_info(self):
        bar_info = multi_process_bar_manager.Namespace()
        bar_info.title = ''
        bar_info.percentage = 0
        return bar_info
