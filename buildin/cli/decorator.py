# -* encoding:utf-8 -*-
""" 装饰器 """
import signal
import time

from buildin.cli.console import Log
from buildin.cli.exception import IRepoTimeoutException


def log_time(func):
    """ 装饰器 记录运行时间 """

    def _wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        run_time = int(end_time - start_time)
        Log.debug("%s run %ss" % (func.__name__, run_time))
        return result

    return _wrapper


def timeout(get_timeout_seconds_func):
    """ timeout decorator """

    def timeout_wrap(func):
        """ wrapper func"""

        def _handle(signum, framework):
            raise IRepoTimeoutException('operation timeout.')

        def invoke_func(*args, **kwargs):
            """ invoke real func """
            signal.signal(signal.SIGALRM, _handle)
            signal.alarm(get_timeout_seconds_func())
            try:
                result = func(*args, **kwargs)
            finally:
                signal.alarm(0)
            return result

        return invoke_func

    return timeout_wrap
