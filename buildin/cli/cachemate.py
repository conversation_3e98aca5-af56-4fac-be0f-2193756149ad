# -* encoding:utf-8 -*-
""" 缓存的meta信息 """
import os

import configparser

from buildin.cli import local_tools
from buildin.cli.filelock import FileLock


class CacheStatus(object):
    """ 缓存状态 """
    NONE = 'NONE'
    INITED = 'INITED'
    FILE_DOWNED = 'FILE_DOWNED'
    MD5_DOWNED = 'MD5_DOWNED'
    MD5_CHECKED = 'MD5_CHECKED'
    FINISHED = 'FINISHED'


class CacheMeta(object):
    """ cache 基本信息(非进程安全) """
    CACHE_HOME = os.path.join(os.path.expanduser('~'), '.irepo', 'cache')
    STATUS_SECTION = 'status'
    STATUS = 'status'
    META_SECTION = 'meta'
    FILE_NAME = 'file_name'
    ARCHIVE_TYPE = 'archive_type'

    def __init__(self, config_file):
        self.config_file = config_file

    def __enter__(self):
        self.lock = FileLock(self.config_file + '.lock')
        self.lock.acquire()

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.lock.release()

    def init_meta(self):
        """ 初始化meta信息 """
        conf = self.__get_conf()
        if not conf.has_section(CacheMeta.STATUS_SECTION):
            conf.add_section(CacheMeta.STATUS_SECTION)
        conf.set(CacheMeta.STATUS_SECTION, CacheMeta.STATUS, CacheStatus.INITED)
        if not conf.has_section(CacheMeta.META_SECTION):
            conf.add_section(CacheMeta.META_SECTION)
        conf.set(CacheMeta.META_SECTION, CacheMeta.FILE_NAME, '.')
        conf.set(CacheMeta.META_SECTION, CacheMeta.ARCHIVE_TYPE, '')
        with open(self.config_file, 'w') as conf_file:
            conf.write(conf_file)

    def set_status(self, cache_status):
        """ 设置缓存状态 """
        conf = self.__get_conf()
        conf.set(CacheMeta.STATUS_SECTION, CacheMeta.STATUS, cache_status)
        self.__save_conf(conf)

    def get_status(self):
        """ 获取缓存状态 """
        if not os.path.exists(self.config_file):
            return CacheStatus.NONE
        conf = self.__get_conf()
        if not conf.has_section(CacheMeta.STATUS_SECTION):
            return CacheStatus.NONE
        if CacheMeta.STATUS not in conf.options(CacheMeta.STATUS_SECTION):
            return CacheStatus.NONE
        return self.__get_conf().get(CacheMeta.STATUS_SECTION, CacheMeta.STATUS)

    def get_archive_type(self):
        """ 获取缓存的制品归档类型 """
        return self.__get_conf().get(CacheMeta.META_SECTION, CacheMeta.ARCHIVE_TYPE)

    def set_archive_type(self, archive_type):
        """ 设置缓存的制品归档类型 """
        conf = self.__get_conf()
        conf.set(CacheMeta.META_SECTION, CacheMeta.ARCHIVE_TYPE, archive_type)
        self.__save_conf(conf)

    def set_file_name(self, file_name):
        """ 设置文件名字 """
        conf = self.__get_conf()
        conf.set(CacheMeta.META_SECTION, CacheMeta.FILE_NAME, file_name)
        self.__save_conf(conf)

    def get_file_name(self):
        """ 获取文件名字 """
        return self.__get_conf().get(CacheMeta.META_SECTION, CacheMeta.FILE_NAME)

    def __get_conf(self):
        conf = configparser.ConfigParser()
        conf.read(self.config_file)
        return conf

    def __save_conf(self, new_conf):
        """ copy on write """
        conf_temp_file = self.config_file + '.tmp'
        with open(conf_temp_file, 'w') as conf_file:
            new_conf.write(conf_file)
        local_tools.exe_cmd("mv -f %s %s" % (conf_temp_file, self.config_file))
