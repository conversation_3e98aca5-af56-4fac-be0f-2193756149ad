# -* encoding:utf-8 -*-
import os
import re
from buildin.cli.console import Log

# e.g.: https://irepo.baidu-int.com/rest/prod/v3/baidu/irepo-auto/common-repo/nodes/1027539/files
agile_download_url_regex = r'.+\/rest\/.+\/nodes\/(?P<nodeId>.+)\/files'
agile_download_url_pattern = re.compile(agile_download_url_regex)


def get_runtime_inject_artifact_from_agile():
    inject_artifacts = Bundle()
    agile_pipeline_type = os.environ.get('AGILE_PIPELINE_TYPE')
    agile_prod_reponame = os.environ.get('AGILE_MODULE_NAME')
    agile_prod_httpurl = os.environ.get('AGILE_PRODUCT_HTTP_URL')
    agile_model_reponame = os.environ.get('AGILE_MODEL_REPO_NAME')
    agile_model_revision = os.environ.get('AGILE_MODEL_REVISION')

    # 单模块场景
    if agile_pipeline_type == 'MODULE':
        prod_artifact = __create_artifact(agile_prod_reponame, agile_prod_httpurl)
        if prod_artifact:
            inject_artifacts.prods.append(prod_artifact)
        else:
            Log.warn("can't parse %s " % agile_prod_httpurl)

    # 非单模块场景
    if agile_pipeline_type in ('AGGREGATION', 'AI_PACK'):
        if agile_model_reponame and agile_model_revision:
            model_repos = agile_model_reponame.split(",")
            model_revisions = agile_model_revision.split(",")
            for reponame, revision in zip(model_repos, model_revisions):
                artifact = __new_artifact(reponame, revision)
                if artifact:
                    inject_artifacts.models.append(artifact)

        if agile_prod_reponame and agile_prod_httpurl:
            prod_repos = agile_prod_reponame.split(",")
            prod_httpurls = agile_prod_httpurl.split(",")
            for reponame, prod_httpurl in zip(prod_repos, prod_httpurls):
                artifact = __create_artifact(reponame, prod_httpurl)
                if artifact:
                    inject_artifacts.prods.append(artifact)
    return inject_artifacts


class Bundle(object):
    def __init__(self):
        self.bundles = []
        self.prods = []
        self.models = []

    def is_empty(self):
        return len(self.bundles) == 0 and len(self.prods) == 0 and len(self.models) == 0


def __create_artifact(reponame, httpurl):
    if not reponame or not httpurl:
        return None
    version_matcher = re.search(agile_download_url_regex, httpurl)
    if version_matcher:
        version = version_matcher.group('nodeId')
        return __new_artifact(reponame, version)
    return None


def __new_artifact(reponame='', version=''):
    if not reponame or not version:
        return None
    return {'reponame': reponame, 'version': version}
