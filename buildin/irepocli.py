# -*- coding:utf-8 -*-
"""buildin client entry point"""
import argparse
import traceback

from buildin.cli import local_tools
from buildin.cli.console import Log, LogLevel
from buildin.cli.exception import IRepoException, IRepoTimeoutException
from buildin.cli.options import GlobalOptions
from buildin.cli.decorator import timeout
from buildin.cli.options import KeySafeDict
from buildin.executors.abstract import IRepoExecutor
from buildin.providers.root_provider import RootProvider
from client import version_update


def get_executor_class_factory():
    """
    get all executor
    key:class name
    value:executor object
    """
    executor_class_factory = {}
    subClasses = IRepoExecutor.__subclasses__()
    for subClz in subClasses:
        executor_class_factory[subClz.__name__] = subClz
    return executor_class_factory


class RepoClient(object):
    """ irepo command wrapper """

    def __init__(self):
        root = RootProvider()
        root_parser = argparse.ArgumentParser(
            prog="repocli", formatter_class=argparse.RawTextHelpFormatter,
        )
        root_sub_parsers = root_parser.add_subparsers(help=root.help)
        self.add_arg(root_sub_parsers, root)
        self.root_parser = root_parser

    def add_arg(self, sub_parsers, provider):
        """ recursive for add arg """
        for sub_command_provider in provider.sub_command_providers:
            sub_parser = sub_parsers.add_parser(sub_command_provider.command_name,
                                                help=sub_command_provider.help)
            sub_command_provider.arg_adder.assemble(sub_parser)
            if sub_command_provider.executor:
                sub_parser.set_defaults(executorClz=sub_command_provider.executor)
            if sub_command_provider.sub_command_providers:
                self.add_arg(sub_parser.add_subparsers(help=sub_command_provider.help),
                             sub_command_provider)

    def parse_args(self):
        """ parse all arguments """
        self.options = self.root_parser.parse_args()
        GlobalOptions.timeout_seconds = self.options.timeout
        GlobalOptions.debug = self.options.debug
        GlobalOptions.quiet = self.options.quiet
        GlobalOptions.no_color = self.options.no_color
        if GlobalOptions.debug:
            Log.set_level(LogLevel.DEBUG)
        if 'disable_giano' in self.options:
            GlobalOptions.disable_giano = self.options.disable_giano
        return self

    @timeout(GlobalOptions.get_timeout_seconds)
    def process_operation(self):
        """ exact command parse from arguments """
        Log.info('client version: %s' % version_update.get_version())
        executor = self.options.executorClz(get_executor_class_factory())
        executor.invoke(KeySafeDict(self.options.__dict__))


def main():
    """ irepo entry """
    exitCode = 0
    try:
        RepoClient().parse_args().process_operation()
    except IRepoException as e:
        Log.error(str(e))
        exitCode = e.code
    except IRepoTimeoutException as e:
        Log.error(str(e))
        exitCode = IRepoException.TIME_OUT
    except KeyboardInterrupt:
        Log.warn('operation cancelled')
        exitCode = IRepoException.USER_CANCEL
    except SystemExit as e:
        exitCode = e.code
    except BaseException as e:
        Log.fatal(e)
        Log.warn('need help? please hi langshiquan')
        exitCode = IRepoException.UNKNOWN
        if GlobalOptions.debug:
            traceback.print_exc()
    finally:
        # if debug and exitCode is not normal, not clean workspace for debug
        if GlobalOptions.debug and exitCode != 0:
            exit(exitCode)
        local_tools.clean_tmp_workspace()
        exit(exitCode)


if __name__ == '__main__':
    main()
